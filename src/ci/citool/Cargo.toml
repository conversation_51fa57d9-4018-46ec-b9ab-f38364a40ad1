[package]
name = "citool"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1"
askama = "0.14"
clap = { version = "4.5", features = ["derive"] }
csv = "1"
diff = "0.1"
glob-match = "0.2"
serde = { version = "1", features = ["derive"] }
serde_yaml = "0.9"
serde_json = "1"
ureq = { version = "3", features = ["json"] }

build_helper = { path = "../../build_helper" }

[dev-dependencies]
insta = "1"

# Tell cargo that citool is its own workspace.
# If this is omitted, cargo will look for a workspace elsewhere.
# We want to avoid this, since citool is independent of the other crates.
[workspace]

# Make compilation faster
[profile.dev]
debug = 0
