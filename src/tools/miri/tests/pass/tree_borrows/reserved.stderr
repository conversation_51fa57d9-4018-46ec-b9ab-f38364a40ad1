[interior mut + protected] Foreign Read: Re* -> Frz
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   1
| Act |    └─┬──<TAG=root of the allocation>
| ReIM|      └─┬──<TAG=base>
| ReIM|        ├─┬──<TAG=x>
| ReIM|        │ └─┬──<TAG=caller:x>
| ResC|        │   └────<TAG=callee:x>
| ReIM|        └────<TAG=y, caller:y, callee:y>
──────────────────────────────────────────────────
[interior mut] Foreign Read: Re* -> Re*
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   8
| Act |    └─┬──<TAG=root of the allocation>
| ReIM|      └─┬──<TAG=base>
| ReIM|        ├────<TAG=x>
| ReIM|        └────<TAG=y>
──────────────────────────────────────────────────
[interior mut] Foreign Write: Re* -> Re*
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   8
| Act |    └─┬──<TAG=root of the allocation>
| Act |      └─┬──<TAG=base>
| ReIM|        ├────<TAG=x>
| Act |        └────<TAG=y>
──────────────────────────────────────────────────
[protected] Foreign Read: Res -> Frz
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   1
| Act |    └─┬──<TAG=root of the allocation>
| Res |      └─┬──<TAG=base>
| Res |        ├─┬──<TAG=x>
| Res |        │ └─┬──<TAG=caller:x>
| ResC|        │   └────<TAG=callee:x>
| Res |        └────<TAG=y, caller:y, callee:y>
──────────────────────────────────────────────────
[] Foreign Read: Res -> Res
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   1
| Act |    └─┬──<TAG=root of the allocation>
| Res |      └─┬──<TAG=base>
| Res |        ├────<TAG=x>
| Res |        └────<TAG=y>
──────────────────────────────────────────────────
[] Foreign Write: Res -> Dis
──────────────────────────────────────────────────
Warning: this tree is indicative only. Some tags may have been hidden.
0..   1
| Act |    └─┬──<TAG=root of the allocation>
| Act |      └─┬──<TAG=base>
| Dis |        ├────<TAG=x>
| Act |        └────<TAG=y>
──────────────────────────────────────────────────
