error: Undefined Behavior: destroyed a locked rwlock
  --> tests/fail-dep/concurrency/libc_pthread_rwlock_destroy_read_locked.rs:LL:CC
   |
LL |         libc::pthread_rwlock_destroy(rw.get());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ destroyed a locked rwlock
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail-dep/concurrency/libc_pthread_rwlock_destroy_read_locked.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

