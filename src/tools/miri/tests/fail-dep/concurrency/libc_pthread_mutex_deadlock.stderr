error: deadlock: the evaluated program deadlocked
  --> tests/fail-dep/concurrency/libc_pthread_mutex_deadlock.rs:LL:CC
   |
LL |             assert_eq!(libc::pthread_mutex_lock(lock_copy.0.get() as *mut _), 0);
   |                                                                            ^ the evaluated program deadlocked
   |
   = note: BACKTRACE on thread `unnamed-ID`:
   = note: inside closure at tests/fail-dep/concurrency/libc_pthread_mutex_deadlock.rs:LL:CC

error: deadlock: the evaluated program deadlocked
  --> RUSTLIB/std/src/sys/pal/PLATFORM/thread.rs:LL:CC
   |
LL |         let ret = unsafe { libc::pthread_join(id, ptr::null_mut()) };
   |                                                                  ^ the evaluated program deadlocked
   |
   = note: BACKTRACE:
   = note: inside `std::sys::pal::PLATFORM::thread::Thread::join` at RUSTLIB/std/src/sys/pal/PLATFORM/thread.rs:LL:CC
   = note: inside `std::thread::JoinInner::<'_, ()>::join` at RUSTLIB/std/src/thread/mod.rs:LL:CC
   = note: inside `std::thread::JoinHandle::<()>::join` at RUSTLIB/std/src/thread/mod.rs:LL:CC
note: inside `main`
  --> tests/fail-dep/concurrency/libc_pthread_mutex_deadlock.rs:LL:CC
   |
LL | /         thread::spawn(move || {
LL | |             assert_eq!(libc::pthread_mutex_lock(lock_copy.0.get() as *mut _), 0);
LL | |         })
LL | |         .join()
   | |_______________^

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 2 previous errors

