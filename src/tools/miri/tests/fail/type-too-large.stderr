error: post-monomorphization error: values of the type `[u8; 2305843011361177600]` are too big for the target architecture
  --> tests/fail/type-too-large.rs:LL:CC
   |
LL |     _fat = [0; (1u64 << 61) as usize + (1u64 << 31) as usize];
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ values of the type `[u8; 2305843011361177600]` are too big for the target architecture
   |
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/type-too-large.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

