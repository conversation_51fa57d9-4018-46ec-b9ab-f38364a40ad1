error: Undefined Behavior: constructing invalid value at .1.my_alloc_field1: encountered uninitialized memory, but expected an integer
  --> tests/fail/validity/box-custom-alloc-invalid-alloc.rs:LL:CC
   |
LL |         std::mem::transmute(b)
   |         ^^^^^^^^^^^^^^^^^^^^^^ constructing invalid value at .1.my_alloc_field1: encountered uninitialized memory, but expected an integer
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/validity/box-custom-alloc-invalid-alloc.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

