error: Undefined Behavior: constructing invalid value: encountered a null function pointer
  --> tests/fail/validity/invalid_fnptr_null.rs:LL:CC
   |
LL |     let _b: fn() = unsafe { std::mem::transmute(0usize) };
   |                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^ constructing invalid value: encountered a null function pointer
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/validity/invalid_fnptr_null.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

