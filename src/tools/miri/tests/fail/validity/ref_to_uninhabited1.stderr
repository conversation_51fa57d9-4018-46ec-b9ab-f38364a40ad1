error: Undefined Behavior: constructing invalid value: encountered a box pointing to uninhabited type !
  --> tests/fail/validity/ref_to_uninhabited1.rs:LL:CC
   |
LL |         let x: Box<!> = transmute(&mut 42);
   |                         ^^^^^^^^^^^^^^^^^^ constructing invalid value: encountered a box pointing to uninhabited type !
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/validity/ref_to_uninhabited1.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

