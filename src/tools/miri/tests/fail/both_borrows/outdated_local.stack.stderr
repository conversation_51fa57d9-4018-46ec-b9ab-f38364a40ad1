error: Undefined Behavior: attempting a read access using <TAG> at ALLOC[0x0], but that tag does not exist in the borrow stack for this location
  --> tests/fail/both_borrows/outdated_local.rs:LL:CC
   |
LL |     assert_eq!(unsafe { *y }, 1);
   |                         ^^
   |                         |
   |                         attempting a read access using <TAG> at ALLOC[0x0], but that tag does not exist in the borrow stack for this location
   |                         this error occurs as part of an access at ALLOC[0x0..0x4]
   |
   = help: this indicates a potential bug in the program: it performed an invalid operation, but the Stacked Borrows rules it violated are still experimental
   = help: see https://github.com/rust-lang/unsafe-code-guidelines/blob/master/wip/stacked-borrows.md for further information
help: <TAG> was created by a SharedReadOnly retag at offsets [0x0..0x4]
  --> tests/fail/both_borrows/outdated_local.rs:LL:CC
   |
LL |     let y: *const i32 = &x;
   |                         ^^
help: <TAG> was later invalidated at offsets [0x0..0x4] by a write access
  --> tests/fail/both_borrows/outdated_local.rs:LL:CC
   |
LL |     x = 1; // this invalidates y by reactivating the lowermost uniq borrow for this local
   |     ^^^^^
   = note: BACKTRACE (of the first span):
   = note: inside `main` at tests/fail/both_borrows/outdated_local.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

