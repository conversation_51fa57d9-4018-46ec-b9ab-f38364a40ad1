error: Undefined Behavior: entering unreachable code
  --> tests/fail/dangling_pointers/dangling_pointer_deref_match_never.rs:LL:CC
   |
LL |         match *p {}
   |               ^^ entering unreachable code
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/dangling_pointers/dangling_pointer_deref_match_never.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

