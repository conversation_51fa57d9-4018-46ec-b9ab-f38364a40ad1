error: Undefined Behavior: bad declaration of miri_resolve_frame - should return a struct with 5 fields
  --> tests/fail/shims/backtrace/bad-backtrace-decl.rs:LL:CC
   |
LL | ...   miri_resolve_frame(*frame, 0);
   |       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ bad declaration of miri_resolve_frame - should return a struct with 5 fields
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/shims/backtrace/bad-backtrace-decl.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

