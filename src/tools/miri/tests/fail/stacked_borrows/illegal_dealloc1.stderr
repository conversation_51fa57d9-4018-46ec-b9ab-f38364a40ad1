error: Undefined Behavior: attempting deallocation using <TAG> at ALLOC, but that tag does not exist in the borrow stack for this location
  --> tests/fail/stacked_borrows/illegal_deALLOC.rs:LL:CC
   |
LL |         dealloc(ptr2, Layout::from_size_align_unchecked(1, 1));
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ attempting deallocation using <TAG> at ALLOC, but that tag does not exist in the borrow stack for this location
   |
   = help: this indicates a potential bug in the program: it performed an invalid operation, but the Stacked Borrows rules it violated are still experimental
   = help: see https://github.com/rust-lang/unsafe-code-guidelines/blob/master/wip/stacked-borrows.md for further information
help: <TAG> was created by a SharedReadWrite retag at offsets [0x0..0x1]
  --> tests/fail/stacked_borrows/illegal_deALLOC.rs:LL:CC
   |
LL |         let ptr2 = (&mut *ptr1) as *mut u8;
   |                    ^^^^^^^^^^^^
help: <TAG> was later invalidated at offsets [0x0..0x1] by a write access
  --> tests/fail/stacked_borrows/illegal_deALLOC.rs:LL:CC
   |
LL |         ptr1.write(0);
   |         ^^^^^^^^^^^^^
   = note: BACKTRACE (of the first span):
   = note: inside `main` at tests/fail/stacked_borrows/illegal_deALLOC.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

