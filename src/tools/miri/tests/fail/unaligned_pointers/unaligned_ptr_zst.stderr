error: Undefined Behavior: accessing memory based on pointer with alignment ALIGN, but alignment ALIGN is required
  --> tests/fail/unaligned_pointers/unaligned_ptr_zst.rs:LL:CC
   |
LL |         let _x = unsafe { *x };
   |                           ^^ accessing memory based on pointer with alignment ALIGN, but alignment ALIGN is required
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/unaligned_pointers/unaligned_ptr_zst.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

