error: Undefined Behavior: Data race detected between (1) non-atomic write on thread `unnamed-ID` and (2) non-atomic write on thread `unnamed-ID` at ALLOC. (2) just happened here
  --> tests/fail/data_race/enable_after_join_to_main.rs:LL:CC
   |
LL |             *c.0 = 64;
   |             ^^^^^^^^^ Data race detected between (1) non-atomic write on thread `unnamed-ID` and (2) non-atomic write on thread `unnamed-ID` at ALLOC. (2) just happened here
   |
help: and (1) occurred earlier here
  --> tests/fail/data_race/enable_after_join_to_main.rs:LL:CC
   |
LL |             *c.0 = 32;
   |             ^^^^^^^^^
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE (of the first span) on thread `unnamed-ID`:
   = note: inside closure at tests/fail/data_race/enable_after_join_to_main.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

