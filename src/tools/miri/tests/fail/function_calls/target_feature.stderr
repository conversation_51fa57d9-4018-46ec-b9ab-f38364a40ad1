error: Undefined Behavior: calling a function that requires unavailable target features: ssse3
  --> tests/fail/function_calls/target_feature.rs:LL:CC
   |
LL |         ssse3_fn();
   |         ^^^^^^^^^^ calling a function that requires unavailable target features: ssse3
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/function_calls/target_feature.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

