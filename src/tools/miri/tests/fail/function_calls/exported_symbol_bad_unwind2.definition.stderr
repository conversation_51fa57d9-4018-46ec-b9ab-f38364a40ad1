
thread 'main' panicked at tests/fail/function_calls/exported_symbol_bad_unwind2.rs:LL:CC:
explicit panic
note: run with `RUST_BACKTRACE=1` environment variable to display a backtrace
note: in Miri, you may have to set `MIRIFLAGS=-Zmiri-env-forward=RUST_BACKTRACE` for the environment variable to have an effect

thread 'main' panicked at RUSTLIB/core/src/panicking.rs:LL:CC:
panic in a function that cannot unwind
stack backtrace:
thread caused non-unwinding panic. aborting.
error: abnormal termination: the program aborted execution
  --> RUSTLIB/std/src/sys/pal/PLATFORM/mod.rs:LL:CC
   |
LL | ABORT()
   | ^ the program aborted execution
   |
   = note: BACKTRACE:
   = note: inside `std::sys::pal::PLATFORM::abort_internal` at RUSTLIB/std/src/sys/pal/PLATFORM/mod.rs:LL:CC
   = note: inside `std::panicking::rust_panic_with_hook` at RUSTLIB/std/src/panicking.rs:LL:CC
   = note: inside closure at RUSTLIB/std/src/panicking.rs:LL:CC
   = note: inside `std::sys::backtrace::__rust_end_short_backtrace::<{closure@std::panicking::begin_panic_handler::{closure#0}}, !>` at RUSTLIB/std/src/sys/backtrace.rs:LL:CC
   = note: inside `std::panicking::begin_panic_handler` at RUSTLIB/std/src/panicking.rs:LL:CC
   = note: inside `core::panicking::panic_nounwind` at RUSTLIB/core/src/panicking.rs:LL:CC
   = note: inside `core::panicking::panic_cannot_unwind` at RUSTLIB/core/src/panicking.rs:LL:CC
note: inside `nounwind`
  --> tests/fail/function_calls/exported_symbol_bad_unwind2.rs:LL:CC
   |
LL | / extern "C-unwind" fn nounwind() {
LL | |     panic!();
LL | | }
   | |_^
note: inside `main`
  --> tests/fail/function_calls/exported_symbol_bad_unwind2.rs:LL:CC
   |
LL |     unsafe { nounwind() }
   | ^

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

