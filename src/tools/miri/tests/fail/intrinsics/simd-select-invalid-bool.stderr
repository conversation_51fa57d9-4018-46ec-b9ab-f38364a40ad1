error: Undefined Behavior: each element of a SIMD mask must be all-0-bits or all-1-bits
  --> tests/fail/intrinsics/simd-select-invalid-bool.rs:LL:CC
   |
LL |         simd_select(x, x, x);
   |         ^^^^^^^^^^^^^^^^^^^^ each element of a SIMD mask must be all-0-bits or all-1-bits
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/intrinsics/simd-select-invalid-bool.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

