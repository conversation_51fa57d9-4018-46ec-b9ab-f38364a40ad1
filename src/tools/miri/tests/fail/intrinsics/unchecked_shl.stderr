error: Undefined Behavior: overflowing shift by 8 in `unchecked_shl`
  --> tests/fail/intrinsics/unchecked_shl.rs:LL:CC
   |
LL |         let _n = 1i8.unchecked_shl(8);
   |                  ^^^^^^^^^^^^^^^^^^^^ overflowing shift by 8 in `unchecked_shl`
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/intrinsics/unchecked_shl.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

