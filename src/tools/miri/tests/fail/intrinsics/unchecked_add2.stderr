error: Undefined Behavior: arithmetic overflow in `unchecked_add`
  --> tests/fail/intrinsics/unchecked_add2.rs:LL:CC
   |
LL |     let _val = unsafe { (-30000i16).unchecked_add(-8000) };
   |                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ arithmetic overflow in `unchecked_add`
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/intrinsics/unchecked_add2.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

