error: unsupported operation: extern static `FOO` is not supported by <PERSON><PERSON>
  --> tests/fail/extern_static.rs:LL:CC
   |
LL |     let _val = std::ptr::addr_of!(FOO);
   |                                   ^^^ extern static `FOO` is not supported by <PERSON><PERSON>
   |
   = help: this is likely not a bug in the program; it indicates that the program performed an operation that <PERSON><PERSON> does not support
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/extern_static.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

