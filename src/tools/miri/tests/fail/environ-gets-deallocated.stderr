error: Undefined Behavior: memory access failed: ALLOC has been freed, so this pointer is dangling
  --> tests/fail/environ-gets-deallocated.rs:LL:CC
   |
LL |     let _y = unsafe { *pointer };
   |                       ^^^^^^^^ memory access failed: ALLOC has been freed, so this pointer is dangling
   |
   = help: this indicates a bug in the program: it performed an invalid operation, and caused Undefined Behavior
   = help: see https://doc.rust-lang.org/nightly/reference/behavior-considered-undefined.html for further information
   = note: BACKTRACE:
   = note: inside `main` at tests/fail/environ-gets-deallocated.rs:LL:CC

note: some details are omitted, run with `MIRIFLAGS=-Zmiri-backtrace=full` for a verbose backtrace

error: aborting due to 1 previous error

