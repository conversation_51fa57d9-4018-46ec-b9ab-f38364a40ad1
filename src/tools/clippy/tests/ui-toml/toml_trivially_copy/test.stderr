error: this argument (N byte) is passed by reference, but would be more efficient if passed by value (limit: N byte)
  --> tests/ui-toml/toml_trivially_copy/test.rs:15:11
   |
LL | fn bad(x: &u16, y: &Foo) {}
   |           ^^^^ help: consider passing by value instead: `u16`
   |
   = note: `-D clippy::trivially-copy-pass-by-ref` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::trivially_copy_pass_by_ref)]`

error: this argument (N byte) is passed by reference, but would be more efficient if passed by value (limit: N byte)
  --> tests/ui-toml/toml_trivially_copy/test.rs:15:20
   |
LL | fn bad(x: &u16, y: &Foo) {}
   |                    ^^^^ help: consider passing by value instead: `Foo`

error: aborting due to 2 previous errors

