error: this `to_owned` call clones the Cow<'_, str> itself and does not cause the Cow<'_, str> contents to become owned
  --> tests/ui/suspicious_to_owned.rs:17:13
   |
LL |     let _ = cow.to_owned();
   |             ^^^^^^^^^^^^^^
   |
   = note: `-D clippy::suspicious-to-owned` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::suspicious_to_owned)]`
help: depending on intent, either make the Cow an Owned variant
   |
LL |     let _ = cow.into_owned();
   |                 ++
help: or clone the Cow itself
   |
LL -     let _ = cow.to_owned();
LL +     let _ = cow.clone();
   |

error: this `to_owned` call clones the Cow<'_, [char; 3]> itself and does not cause the Cow<'_, [char; 3]> contents to become owned
  --> tests/ui/suspicious_to_owned.rs:29:13
   |
LL |     let _ = cow.to_owned();
   |             ^^^^^^^^^^^^^^
   |
help: depending on intent, either make the Cow an Owned variant
   |
LL |     let _ = cow.into_owned();
   |                 ++
help: or clone the Cow itself
   |
LL -     let _ = cow.to_owned();
LL +     let _ = cow.clone();
   |

error: this `to_owned` call clones the Cow<'_, Vec<char>> itself and does not cause the Cow<'_, Vec<char>> contents to become owned
  --> tests/ui/suspicious_to_owned.rs:41:13
   |
LL |     let _ = cow.to_owned();
   |             ^^^^^^^^^^^^^^
   |
help: depending on intent, either make the Cow an Owned variant
   |
LL |     let _ = cow.into_owned();
   |                 ++
help: or clone the Cow itself
   |
LL -     let _ = cow.to_owned();
LL +     let _ = cow.clone();
   |

error: this `to_owned` call clones the Cow<'_, str> itself and does not cause the Cow<'_, str> contents to become owned
  --> tests/ui/suspicious_to_owned.rs:53:13
   |
LL |     let _ = cow.to_owned();
   |             ^^^^^^^^^^^^^^
   |
help: depending on intent, either make the Cow an Owned variant
   |
LL |     let _ = cow.into_owned();
   |                 ++
help: or clone the Cow itself
   |
LL -     let _ = cow.to_owned();
LL +     let _ = cow.clone();
   |

error: implicitly cloning a `String` by calling `to_owned` on its dereferenced type
  --> tests/ui/suspicious_to_owned.rs:69:13
   |
LL |     let _ = String::from(moo).to_owned();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `String::from(moo).clone()`
   |
   = note: `-D clippy::implicit-clone` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::implicit_clone)]`

error: implicitly cloning a `Vec` by calling `to_owned` on its dereferenced type
  --> tests/ui/suspicious_to_owned.rs:72:13
   |
LL |     let _ = moos_vec.to_owned();
   |             ^^^^^^^^^^^^^^^^^^^ help: consider using: `moos_vec.clone()`

error: aborting due to 6 previous errors

