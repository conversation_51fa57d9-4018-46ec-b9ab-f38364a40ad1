error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:9:5
   |
LL | /     if a.is_none() {
LL | |
LL | |         return None;
LL | |     }
   | |_____^ help: replace it with: `a?;`
   |
   = note: `-D clippy::question-mark` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::question_mark)]`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:55:9
   |
LL | /         if (self.opt).is_none() {
LL | |
LL | |             return None;
LL | |         }
   | |_________^ help: replace it with: `(self.opt)?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:60:9
   |
LL | /         if self.opt.is_none() {
LL | |
LL | |             return None
LL | |         }
   | |_________^ help: replace it with: `self.opt?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:65:17
   |
LL |           let _ = if self.opt.is_none() {
   |  _________________^
LL | |
LL | |             return None;
LL | |         } else {
LL | |             self.opt
LL | |         };
   | |_________^ help: replace it with: `Some(self.opt?)`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:72:17
   |
LL |           let _ = if let Some(x) = self.opt {
   |  _________________^
LL | |
LL | |             x
LL | |         } else {
LL | |             return None;
LL | |         };
   | |_________^ help: replace it with: `self.opt?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:90:9
   |
LL | /         if self.opt.is_none() {
LL | |
LL | |             return None;
LL | |         }
   | |_________^ help: replace it with: `self.opt.as_ref()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:99:9
   |
LL | /         if self.opt.is_none() {
LL | |
LL | |             return None;
LL | |         }
   | |_________^ help: replace it with: `self.opt.as_ref()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:108:9
   |
LL | /         if self.opt.is_none() {
LL | |
LL | |             return None;
LL | |         }
   | |_________^ help: replace it with: `self.opt.as_ref()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:116:26
   |
LL |           let v: &Vec<_> = if let Some(ref v) = self.opt {
   |  __________________________^
LL | |
LL | |             v
LL | |         } else {
LL | |             return None;
LL | |         };
   | |_________^ help: replace it with: `self.opt.as_ref()?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:127:17
   |
LL |           let v = if let Some(v) = self.opt {
   |  _________________^
LL | |
LL | |             v
LL | |         } else {
LL | |             return None;
LL | |         };
   | |_________^ help: replace it with: `self.opt?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:149:5
   |
LL | /     if f().is_none() {
LL | |
LL | |         return None;
LL | |     }
   | |_____^ help: replace it with: `f()?;`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:154:16
   |
LL |       let _val = match f() {
   |  ________________^
LL | |
LL | |         Some(val) => val,
LL | |         None => return None,
LL | |     };
   | |_____^ help: try instead: `f()?`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:165:5
   |
LL | /     match f() {
LL | |
LL | |         Some(val) => val,
LL | |         None => return None,
LL | |     };
   | |_____^ help: try instead: `f()?`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:171:5
   |
LL | /     match opt_none!() {
LL | |
LL | |         Some(x) => x,
LL | |         None => return None,
LL | |     };
   | |_____^ help: try instead: `opt_none!()?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:198:13
   |
LL |     let _ = if let Ok(x) = x { x } else { return x };
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace it with: `x?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:201:5
   |
LL | /     if x.is_err() {
LL | |
LL | |         return x;
LL | |     }
   | |_____^ help: replace it with: `x?;`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:206:16
   |
LL |       let _val = match func_returning_result() {
   |  ________________^
LL | |
LL | |         Ok(val) => val,
LL | |         Err(err) => return Err(err),
LL | |     };
   | |_____^ help: try instead: `func_returning_result()?`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:212:5
   |
LL | /     match func_returning_result() {
LL | |
LL | |         Ok(val) => val,
LL | |         Err(err) => return Err(err),
LL | |     };
   | |_____^ help: try instead: `func_returning_result()?`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:304:5
   |
LL | /     if let Err(err) = func_returning_result() {
LL | |
LL | |         return Err(err);
LL | |     }
   | |_____^ help: replace it with: `func_returning_result()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:312:5
   |
LL | /     if let Err(err) = func_returning_result() {
LL | |
LL | |         return Err(err);
LL | |     }
   | |_____^ help: replace it with: `func_returning_result()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:395:13
   |
LL | /             if a.is_none() {
LL | |
LL | |                 return None;
...  |
LL | |             }
   | |_____________^ help: replace it with: `a?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:456:5
   |
LL | /     let Some(v) = bar.foo.owned.clone() else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let v = bar.foo.owned.clone()?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:471:5
   |
LL | /     let Some(ref x) = foo.opt_x else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let x = foo.opt_x.as_ref()?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:481:5
   |
LL | /     let Some(ref mut x) = foo.opt_x else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let x = foo.opt_x.as_mut()?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:492:5
   |
LL | /     let Some(ref x @ ref y) = foo.opt_x else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let x @ y = foo.opt_x.as_ref()?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:496:5
   |
LL | /     let Some(ref x @ WrapperStructWithString(_)) = bar else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let x @ &WrapperStructWithString(_) = bar.as_ref()?;`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:500:5
   |
LL | /     let Some(ref mut x @ WrapperStructWithString(_)) = bar else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let x @ &mut WrapperStructWithString(_) = bar.as_mut()?;`

error: this block may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:522:5
   |
LL | /     if arg.is_none() {
LL | |
LL | |         return None;
LL | |     }
   | |_____^ help: replace it with: `arg?;`

error: this `match` expression can be replaced with `?`
  --> tests/ui/question_mark.rs:526:15
   |
LL |       let val = match arg {
   |  _______________^
LL | |
LL | |         Some(val) => val,
LL | |         None => return None,
LL | |     };
   | |_____^ help: try instead: `arg?`

error: this `let...else` may be rewritten with the `?` operator
  --> tests/ui/question_mark.rs:536:5
   |
LL | /     let Some(a) = *a else {
LL | |         return None;
LL | |     };
   | |______^ help: replace it with: `let a = (*a)?;`

error: aborting due to 30 previous errors

