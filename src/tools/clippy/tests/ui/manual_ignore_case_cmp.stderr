error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:15:8
   |
LL |     if a.to_ascii_lowercase() == b.to_ascii_lowercase() {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = note: `-D clippy::manual-ignore-case-cmp` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::manual_ignore_case_cmp)]`
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     if a.to_ascii_lowercase() == b.to_ascii_lowercase() {
LL +     if a.eq_ignore_ascii_case(b) {
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:19:8
   |
LL |     if a.to_ascii_uppercase() == b.to_ascii_uppercase() {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     if a.to_ascii_uppercase() == b.to_ascii_uppercase() {
LL +     if a.eq_ignore_ascii_case(b) {
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:23:13
   |
LL |     let r = a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     let r = a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     let r = a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:25:18
   |
LL |     let r = r || a.to_ascii_uppercase() == b.to_ascii_uppercase();
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     let r = r || a.to_ascii_uppercase() == b.to_ascii_uppercase();
LL +     let r = r || a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:27:10
   |
LL |     r && a.to_ascii_lowercase() == b.to_uppercase().to_ascii_lowercase();
   |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     r && a.to_ascii_lowercase() == b.to_uppercase().to_ascii_lowercase();
LL +     r && a.eq_ignore_ascii_case(&b.to_uppercase());
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:30:8
   |
LL |     if a.to_ascii_lowercase() != b.to_ascii_lowercase() {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     if a.to_ascii_lowercase() != b.to_ascii_lowercase() {
LL +     if !a.eq_ignore_ascii_case(b) {
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:34:8
   |
LL |     if a.to_ascii_uppercase() != b.to_ascii_uppercase() {
   |        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     if a.to_ascii_uppercase() != b.to_ascii_uppercase() {
LL +     if !a.eq_ignore_ascii_case(b) {
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:38:13
   |
LL |     let r = a.to_ascii_lowercase() != b.to_ascii_lowercase();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     let r = a.to_ascii_lowercase() != b.to_ascii_lowercase();
LL +     let r = !a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:40:18
   |
LL |     let r = r || a.to_ascii_uppercase() != b.to_ascii_uppercase();
   |                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     let r = r || a.to_ascii_uppercase() != b.to_ascii_uppercase();
LL +     let r = r || !a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:42:10
   |
LL |     r && a.to_ascii_lowercase() != b.to_uppercase().to_ascii_lowercase();
   |          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     r && a.to_ascii_lowercase() != b.to_uppercase().to_ascii_lowercase();
LL +     r && !a.eq_ignore_ascii_case(&b.to_uppercase());
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:54:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:58:5
   |
LL |     a.to_ascii_lowercase() == 'a';
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == 'a';
LL +     a.eq_ignore_ascii_case(&'a');
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:60:5
   |
LL |     'a' == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     'a' == b.to_ascii_lowercase();
LL +     'a'.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:64:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:66:5
   |
LL |     a.to_ascii_lowercase() == b'a';
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b'a';
LL +     a.eq_ignore_ascii_case(&b'a');
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:68:5
   |
LL |     b'a' == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b'a' == b.to_ascii_lowercase();
LL +     b'a'.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:72:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:74:5
   |
LL |     a.to_uppercase().to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_uppercase().to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.to_uppercase().eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:76:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:78:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:82:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:84:5
   |
LL |     a.to_uppercase().to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_uppercase().to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.to_uppercase().eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:86:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:88:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:92:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:94:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:96:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:100:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:102:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:106:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:108:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:110:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:113:5
   |
LL |     b.to_ascii_lowercase() == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == a.to_ascii_lowercase();
LL +     b.eq_ignore_ascii_case(&a);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:115:5
   |
LL |     b.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == "a";
LL +     b.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:117:5
   |
LL |     "a" == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == a.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(&a);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:121:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:123:5
   |
LL |     a.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == "a";
LL +     a.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:125:5
   |
LL |     "a" == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == b.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:128:5
   |
LL |     b.to_ascii_lowercase() == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == a.to_ascii_lowercase();
LL +     b.eq_ignore_ascii_case(&a);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:130:5
   |
LL |     b.to_ascii_lowercase() == "a";
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == "a";
LL +     b.eq_ignore_ascii_case("a");
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:132:5
   |
LL |     "a" == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     "a" == a.to_ascii_lowercase();
LL +     "a".eq_ignore_ascii_case(&a);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:136:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:140:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(&b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:144:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:146:5
   |
LL |     b.to_ascii_lowercase() == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == a.to_ascii_lowercase();
LL +     b.eq_ignore_ascii_case(&a);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:150:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:154:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:158:5
   |
LL |     a.to_ascii_lowercase() == b.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     a.to_ascii_lowercase() == b.to_ascii_lowercase();
LL +     a.eq_ignore_ascii_case(b);
   |

error: manual case-insensitive ASCII comparison
  --> tests/ui/manual_ignore_case_cmp.rs:160:5
   |
LL |     b.to_ascii_lowercase() == a.to_ascii_lowercase();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: consider using `.eq_ignore_ascii_case()` instead
   |
LL -     b.to_ascii_lowercase() == a.to_ascii_lowercase();
LL +     b.eq_ignore_ascii_case(a);
   |

error: aborting due to 49 previous errors

