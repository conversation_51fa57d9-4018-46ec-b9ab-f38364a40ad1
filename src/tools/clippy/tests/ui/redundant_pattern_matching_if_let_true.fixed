#![warn(clippy::redundant_pattern_matching)]
#![allow(clippy::needless_if, clippy::no_effect, clippy::nonminimal_bool)]

macro_rules! condition {
    () => {
        true
    };
}

macro_rules! lettrue {
    (if) => {
        if let true = true {}
    };
    (while) => {
        while let true = true {}
    };
}

fn main() {
    let mut k = 5;

    if k > 1 {}
    //~^ redundant_pattern_matching
    if !(k > 5) {}
    //~^ redundant_pattern_matching
    if k > 1 {}
    //~^ redundant_pattern_matching
    if let (true, true) = (k > 1, k > 2) {}
    while k > 1 {
        //~^ redundant_pattern_matching
        k += 1;
    }
    while condition!() {
        //~^ redundant_pattern_matching
        k += 1;
    }

    k > 5;
    //~^ redundant_pattern_matching
    !(k > 5);
    //~^ redundant_pattern_matching
    // Whole loop is from a macro expansion, don't lint:
    lettrue!(if);
    lettrue!(while);
}
