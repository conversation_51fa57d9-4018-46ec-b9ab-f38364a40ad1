error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:18:33
   |
LL |         *unsafe { Box::from_raw(Box::into_raw(Box::new(o)) as *mut super::issue_11278_a::T<String>) }
   |                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `Box::into_raw(Box::new(o)).cast::<super::issue_11278_a::T<String>>()`
   |
   = note: `-D clippy::ptr-as-ptr` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::ptr_as_ptr)]`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:28:13
   |
LL |     let _ = ptr as *const i32;
   |             ^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `ptr.cast::<i32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:30:13
   |
LL |     let _ = mut_ptr as *mut i32;
   |             ^^^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `mut_ptr.cast::<i32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:36:17
   |
LL |         let _ = *ptr_ptr as *const i32;
   |                 ^^^^^^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `(*ptr_ptr).cast::<i32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:50:25
   |
LL |     let _: *const i32 = ptr as *const _;
   |                         ^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `ptr.cast()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:52:23
   |
LL |     let _: *mut i32 = mut_ptr as _;
   |                       ^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `mut_ptr.cast()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:56:21
   |
LL |     let _ = inline!($ptr as *const i32);
   |                     ^^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `$ptr.cast::<i32>()`
   |
   = note: this error originates in the macro `__inline_mac_fn_main` (in Nightly builds, run with -Z macro-backtrace for more info)

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:78:13
   |
LL |     let _ = ptr as *const i32;
   |             ^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `ptr.cast::<i32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:80:13
   |
LL |     let _ = mut_ptr as *mut i32;
   |             ^^^^^^^^^^^^^^^^^^^ help: try `pointer::cast`, a safer alternative: `mut_ptr.cast::<i32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:88:9
   |
LL |         ptr::null_mut() as *mut u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:93:9
   |
LL |         std::ptr::null_mut() as *mut u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null_mut::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:99:9
   |
LL |         ptr::null_mut() as *mut u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:104:9
   |
LL |         core::ptr::null_mut() as *mut u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null_mut::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:110:9
   |
LL |         ptr::null() as *const u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:115:9
   |
LL |         std::ptr::null() as *const u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:121:9
   |
LL |         ptr::null() as *const u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:126:9
   |
LL |         core::ptr::null() as *const u32
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null::<u32>()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:134:9
   |
LL |         ptr::null_mut() as *mut _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:139:9
   |
LL |         std::ptr::null_mut() as *mut _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:145:9
   |
LL |         ptr::null_mut() as *mut _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:150:9
   |
LL |         core::ptr::null_mut() as *mut _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:156:9
   |
LL |         ptr::null() as *const _
   |         ^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:161:9
   |
LL |         std::ptr::null() as *const _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:167:9
   |
LL |         ptr::null() as *const _
   |         ^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:172:9
   |
LL |         core::ptr::null() as *const _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:180:9
   |
LL |         ptr::null_mut() as _
   |         ^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:185:9
   |
LL |         std::ptr::null_mut() as _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:191:9
   |
LL |         ptr::null_mut() as _
   |         ^^^^^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:196:9
   |
LL |         core::ptr::null_mut() as _
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null_mut()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:202:9
   |
LL |         ptr::null() as _
   |         ^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:207:9
   |
LL |         std::ptr::null() as _
   |         ^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `std::ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:213:9
   |
LL |         ptr::null() as _
   |         ^^^^^^^^^^^^^^^^ help: try call directly: `ptr::null()`

error: `as` casting between raw pointers without changing their constness
  --> tests/ui/ptr_as_ptr.rs:218:9
   |
LL |         core::ptr::null() as _
   |         ^^^^^^^^^^^^^^^^^^^^^^ help: try call directly: `core::ptr::null()`

error: aborting due to 33 previous errors

