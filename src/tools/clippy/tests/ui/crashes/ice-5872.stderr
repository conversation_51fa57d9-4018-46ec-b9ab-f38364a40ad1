error: avoid using `collect()` when not needed
  --> tests/ui/crashes/ice-5872.rs:4:39
   |
LL |     let _ = vec![1, 2, 3].into_iter().collect::<Vec<_>>().is_empty();
   |                                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: replace with: `next().is_none()`
   |
   = note: `-D clippy::needless-collect` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::needless_collect)]`

error: aborting due to 1 previous error

