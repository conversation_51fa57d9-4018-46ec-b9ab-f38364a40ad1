error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:14:12
   |
LL |     if let Ok(_) = &result {}
   |     -------^^^^^---------- help: try: `if result.is_ok()`
   |
   = note: `-D clippy::redundant-pattern-matching` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::redundant_pattern_matching)]`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:17:12
   |
LL |     if let Ok(_) = Ok::<i32, i32>(42) {}
   |     -------^^^^^--------------------- help: try: `if Ok::<i32, i32>(42).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:20:12
   |
LL |     if let Err(_) = Err::<i32, i32>(42) {}
   |     -------^^^^^^---------------------- help: try: `if Err::<i32, i32>(42).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:23:15
   |
LL |     while let Ok(_) = Ok::<i32, i32>(10) {}
   |     ----------^^^^^--------------------- help: try: `while Ok::<i32, i32>(10).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:26:15
   |
LL |     while let Err(_) = Ok::<i32, i32>(10) {}
   |     ----------^^^^^^--------------------- help: try: `while Ok::<i32, i32>(10).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:37:5
   |
LL | /     match Ok::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => true,
LL | |         Err(_) => false,
LL | |     };
   | |_____^ help: try: `Ok::<i32, i32>(42).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:43:5
   |
LL | /     match Ok::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => false,
LL | |         Err(_) => true,
LL | |     };
   | |_____^ help: try: `Ok::<i32, i32>(42).is_err()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:49:5
   |
LL | /     match Err::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => false,
LL | |         Err(_) => true,
LL | |     };
   | |_____^ help: try: `Err::<i32, i32>(42).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:55:5
   |
LL | /     match Err::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => true,
LL | |         Err(_) => false,
LL | |     };
   | |_____^ help: try: `Err::<i32, i32>(42).is_ok()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:61:20
   |
LL |     let _ = if let Ok(_) = Ok::<usize, ()>(4) { true } else { false };
   |             -------^^^^^--------------------- help: try: `if Ok::<usize, ()>(4).is_ok()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:70:20
   |
LL |     let _ = if let Ok(_) = gen_res() {
   |             -------^^^^^------------ help: try: `if gen_res().is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:73:19
   |
LL |     } else if let Err(_) = gen_res() {
   |            -------^^^^^^------------ help: try: `if gen_res().is_err()`

error: redundant pattern matching, consider using `is_some()`
  --> tests/ui/redundant_pattern_matching_result.rs:97:19
   |
LL |         while let Some(_) = r#try!(result_opt()) {}
   |         ----------^^^^^^^----------------------- help: try: `while r#try!(result_opt()).is_some()`

error: redundant pattern matching, consider using `is_some()`
  --> tests/ui/redundant_pattern_matching_result.rs:99:16
   |
LL |         if let Some(_) = r#try!(result_opt()) {}
   |         -------^^^^^^^----------------------- help: try: `if r#try!(result_opt()).is_some()`

error: redundant pattern matching, consider using `is_some()`
  --> tests/ui/redundant_pattern_matching_result.rs:106:12
   |
LL |     if let Some(_) = m!() {}
   |     -------^^^^^^^------- help: try: `if m!().is_some()`

error: redundant pattern matching, consider using `is_some()`
  --> tests/ui/redundant_pattern_matching_result.rs:108:15
   |
LL |     while let Some(_) = m!() {}
   |     ----------^^^^^^^------- help: try: `while m!().is_some()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:127:12
   |
LL |     if let Ok(_) = Ok::<i32, i32>(42) {}
   |     -------^^^^^--------------------- help: try: `if Ok::<i32, i32>(42).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:130:12
   |
LL |     if let Err(_) = Err::<i32, i32>(42) {}
   |     -------^^^^^^---------------------- help: try: `if Err::<i32, i32>(42).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:133:15
   |
LL |     while let Ok(_) = Ok::<i32, i32>(10) {}
   |     ----------^^^^^--------------------- help: try: `while Ok::<i32, i32>(10).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:136:15
   |
LL |     while let Err(_) = Ok::<i32, i32>(10) {}
   |     ----------^^^^^^--------------------- help: try: `while Ok::<i32, i32>(10).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:139:5
   |
LL | /     match Ok::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => true,
LL | |         Err(_) => false,
LL | |     };
   | |_____^ help: try: `Ok::<i32, i32>(42).is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:145:5
   |
LL | /     match Err::<i32, i32>(42) {
LL | |
LL | |         Ok(_) => false,
LL | |         Err(_) => true,
LL | |     };
   | |_____^ help: try: `Err::<i32, i32>(42).is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:156:5
   |
LL | /     match x {
LL | |
LL | |         Ok(_) => true,
LL | |         _ => false,
LL | |     };
   | |_____^ help: try: `x.is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:162:5
   |
LL | /     match x {
LL | |
LL | |         Ok(_) => false,
LL | |         _ => true,
LL | |     };
   | |_____^ help: try: `x.is_err()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:168:5
   |
LL | /     match x {
LL | |
LL | |         Err(_) => true,
LL | |         _ => false,
LL | |     };
   | |_____^ help: try: `x.is_err()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:174:5
   |
LL | /     match x {
LL | |
LL | |         Err(_) => false,
LL | |         _ => true,
LL | |     };
   | |_____^ help: try: `x.is_ok()`

error: redundant pattern matching, consider using `is_ok()`
  --> tests/ui/redundant_pattern_matching_result.rs:196:13
   |
LL |     let _ = matches!(x, Ok(_));
   |             ^^^^^^^^^^^^^^^^^^ help: try: `x.is_ok()`

error: redundant pattern matching, consider using `is_err()`
  --> tests/ui/redundant_pattern_matching_result.rs:199:13
   |
LL |     let _ = matches!(x, Err(_));
   |             ^^^^^^^^^^^^^^^^^^^ help: try: `x.is_err()`

error: aborting due to 28 previous errors

