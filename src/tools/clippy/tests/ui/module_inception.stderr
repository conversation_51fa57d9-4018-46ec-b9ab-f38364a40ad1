error: module has the same name as its containing module
  --> tests/ui/module_inception.rs:5:9
   |
LL | /         pub mod bar2 {
LL | |
LL | |
LL | |             pub mod foo2 {}
LL | |         }
   | |_________^
   |
   = note: `-D clippy::module-inception` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::module_inception)]`

error: module has the same name as its containing module
  --> tests/ui/module_inception.rs:12:5
   |
LL | /     pub mod foo2 {
LL | |
LL | |
LL | |         pub mod bar2 {}
LL | |     }
   | |_____^

error: module has the same name as its containing module
  --> tests/ui/module_inception.rs:21:9
   |
LL | /         mod bar {
LL | |
LL | |
LL | |             mod foo {}
LL | |         }
   | |_________^

error: module has the same name as its containing module
  --> tests/ui/module_inception.rs:28:5
   |
LL | /     mod foo {
LL | |
LL | |
LL | |         mod bar {}
LL | |     }
   | |_____^

error: aborting due to 4 previous errors

