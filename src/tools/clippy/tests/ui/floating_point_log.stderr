error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:11:13
   |
LL |     let _ = x.log(2f32);
   |             ^^^^^^^^^^^ help: consider using: `x.log2()`
   |
   = note: `-D clippy::suboptimal-flops` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::suboptimal_flops)]`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:13:13
   |
LL |     let _ = x.log(10f32);
   |             ^^^^^^^^^^^^ help: consider using: `x.log10()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:15:13
   |
LL |     let _ = x.log(std::f32::consts::E);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.ln()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:17:13
   |
LL |     let _ = x.log(TWO);
   |             ^^^^^^^^^^ help: consider using: `x.log2()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:19:13
   |
LL |     let _ = x.log(E);
   |             ^^^^^^^^ help: consider using: `x.ln()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:21:13
   |
LL |     let _ = (x as f32).log(2f32);
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x as f32).log2()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:25:13
   |
LL |     let _ = x.log(2f64);
   |             ^^^^^^^^^^^ help: consider using: `x.log2()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:27:13
   |
LL |     let _ = x.log(10f64);
   |             ^^^^^^^^^^^^ help: consider using: `x.log10()`

error: logarithm for bases 2, 10 and e can be computed more accurately
  --> tests/ui/floating_point_log.rs:29:13
   |
LL |     let _ = x.log(std::f64::consts::E);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.ln()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:35:13
   |
LL |     let _ = (1f32 + 2.).ln();
   |             ^^^^^^^^^^^^^^^^ help: consider using: `2.0f32.ln_1p()`
   |
   = note: `-D clippy::imprecise-flops` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::imprecise_flops)]`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:37:13
   |
LL |     let _ = (1f32 + 2.0).ln();
   |             ^^^^^^^^^^^^^^^^^ help: consider using: `2.0f32.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:39:13
   |
LL |     let _ = (1.0 + x).ln();
   |             ^^^^^^^^^^^^^^ help: consider using: `x.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:41:13
   |
LL |     let _ = (1.0 + x / 2.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x / 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:43:13
   |
LL |     let _ = (1.0 + x.powi(3)).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.powi(3).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:45:13
   |
LL |     let _ = (1.0 + x.powi(3) / 2.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x.powi(3) / 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:47:13
   |
LL |     let _ = (1.0 + (std::f32::consts::E - 1.0)).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `(std::f32::consts::E - 1.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:49:13
   |
LL |     let _ = (x + 1.0).ln();
   |             ^^^^^^^^^^^^^^ help: consider using: `x.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:51:13
   |
LL |     let _ = (x.powi(3) + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.powi(3).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:53:13
   |
LL |     let _ = (x + 2.0 + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x + 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:55:13
   |
LL |     let _ = (x / 2.0 + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x / 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:64:13
   |
LL |     let _ = (1f64 + 2.).ln();
   |             ^^^^^^^^^^^^^^^^ help: consider using: `2.0f64.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:66:13
   |
LL |     let _ = (1f64 + 2.0).ln();
   |             ^^^^^^^^^^^^^^^^^ help: consider using: `2.0f64.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:68:13
   |
LL |     let _ = (1.0 + x).ln();
   |             ^^^^^^^^^^^^^^ help: consider using: `x.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:70:13
   |
LL |     let _ = (1.0 + x / 2.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x / 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:72:13
   |
LL |     let _ = (1.0 + x.powi(3)).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.powi(3).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:74:13
   |
LL |     let _ = (x + 1.0).ln();
   |             ^^^^^^^^^^^^^^ help: consider using: `x.ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:76:13
   |
LL |     let _ = (x.powi(3) + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^^^ help: consider using: `x.powi(3).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:78:13
   |
LL |     let _ = (x + 2.0 + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x + 2.0).ln_1p()`

error: ln(1 + x) can be computed more accurately
  --> tests/ui/floating_point_log.rs:80:13
   |
LL |     let _ = (x / 2.0 + 1.0).ln();
   |             ^^^^^^^^^^^^^^^^^^^^ help: consider using: `(x / 2.0).ln_1p()`

error: aborting due to 29 previous errors

