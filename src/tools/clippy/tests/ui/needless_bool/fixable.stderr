error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:41:5
   |
LL | /     if x {
LL | |         true
LL | |     } else {
LL | |         false
LL | |     };
   | |_____^ help: you can reduce it to: `x`
   |
   = note: `-D clippy::needless-bool` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::needless_bool)]`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:47:5
   |
LL | /     if x {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `!x`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:53:5
   |
LL | /     if x && y {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `!(x && y)`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:62:5
   |
LL | /     if a == b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a != b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:68:5
   |
LL | /     if a != b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a == b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:74:5
   |
LL | /     if a < b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a >= b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:80:5
   |
LL | /     if a <= b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a > b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:86:5
   |
LL | /     if a > b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a <= b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:92:5
   |
LL | /     if a >= b {
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `a < b`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:121:5
   |
LL | /     if x {
LL | |         return true;
LL | |     } else {
LL | |         return false;
LL | |     };
   | |_____^ help: you can reduce it to: `return x`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:130:5
   |
LL | /     if x {
LL | |         return false;
LL | |     } else {
LL | |         return true;
LL | |     };
   | |_____^ help: you can reduce it to: `return !x`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:139:5
   |
LL | /     if x && y {
LL | |         return true;
LL | |     } else {
LL | |         return false;
LL | |     };
   | |_____^ help: you can reduce it to: `return x && y`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:148:5
   |
LL | /     if x && y {
LL | |         return false;
LL | |     } else {
LL | |         return true;
LL | |     };
   | |_____^ help: you can reduce it to: `return !(x && y)`

error: equality checks against true are unnecessary
  --> tests/ui/needless_bool/fixable.rs:157:8
   |
LL |     if x == true {};
   |        ^^^^^^^^^ help: try simplifying it as shown: `x`
   |
   = note: `-D clippy::bool-comparison` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::bool_comparison)]`

error: equality checks against false can be replaced by a negation
  --> tests/ui/needless_bool/fixable.rs:162:8
   |
LL |     if x == false {};
   |        ^^^^^^^^^^ help: try simplifying it as shown: `!x`

error: equality checks against true are unnecessary
  --> tests/ui/needless_bool/fixable.rs:173:8
   |
LL |     if x == true {};
   |        ^^^^^^^^^ help: try simplifying it as shown: `x`

error: equality checks against false can be replaced by a negation
  --> tests/ui/needless_bool/fixable.rs:175:8
   |
LL |     if x == false {};
   |        ^^^^^^^^^^ help: try simplifying it as shown: `!x`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:185:12
   |
LL |       } else if returns_bool() {
   |  ____________^
LL | |         false
LL | |     } else {
LL | |         true
LL | |     };
   | |_____^ help: you can reduce it to: `{ !returns_bool() }`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:199:5
   |
LL | /     if unsafe { no(4) } & 1 != 0 {
LL | |         true
LL | |     } else {
LL | |         false
LL | |     };
   | |_____^ help: you can reduce it to: `(unsafe { no(4) } & 1 != 0)`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:205:30
   |
LL |     let _brackets_unneeded = if unsafe { no(4) } & 1 != 0 { true } else { false };
   |                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: you can reduce it to: `unsafe { no(4) } & 1 != 0`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:209:9
   |
LL |         if unsafe { no(4) } & 1 != 0 { true } else { false }
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: you can reduce it to: `(unsafe { no(4) } & 1 != 0)`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:221:14
   |
LL |     let _x = if a && b { true } else { false }.then(|| todo!());
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: you can reduce it to: `(a && b)`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:223:14
   |
LL |     let _x = if a && b { true } else { false } as u8;
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: you can reduce it to: `(a && b)`

error: this if-then-else expression returns a bool literal
  --> tests/ui/needless_bool/fixable.rs:227:14
   |
LL |     let _x = if a { true } else { false }.then(|| todo!());
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: you can reduce it to: `a`

error: aborting due to 24 previous errors

