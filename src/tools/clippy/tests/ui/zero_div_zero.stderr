error: constant division of `0.0` with `0.0` will always result in NaN
  --> tests/ui/zero_div_zero.rs:4:15
   |
LL |     let nan = 0.0 / 0.0;
   |               ^^^^^^^^^
   |
   = help: consider using `f64::NAN` if you would like a constant representing NaN
   = note: `-D clippy::zero-divided-by-zero` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::zero_divided_by_zero)]`

error: constant division of `0.0` with `0.0` will always result in NaN
  --> tests/ui/zero_div_zero.rs:7:19
   |
LL |     let f64_nan = 0.0 / 0.0f64;
   |                   ^^^^^^^^^^^^
   |
   = help: consider using `f64::NAN` if you would like a constant representing NaN

error: constant division of `0.0` with `0.0` will always result in NaN
  --> tests/ui/zero_div_zero.rs:10:25
   |
LL |     let other_f64_nan = 0.0f64 / 0.0;
   |                         ^^^^^^^^^^^^
   |
   = help: consider using `f64::NAN` if you would like a constant representing NaN

error: constant division of `0.0` with `0.0` will always result in NaN
  --> tests/ui/zero_div_zero.rs:13:28
   |
LL |     let one_more_f64_nan = 0.0f64 / 0.0f64;
   |                            ^^^^^^^^^^^^^^^
   |
   = help: consider using `f64::NAN` if you would like a constant representing NaN

error: aborting due to 4 previous errors

