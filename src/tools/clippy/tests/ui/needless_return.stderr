error: unneeded `return` statement
  --> tests/ui/needless_return.rs:30:5
   |
LL |     return true;
   |     ^^^^^^^^^^^
   |
   = note: `-D clippy::needless-return` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::needless_return)]`
help: remove `return`
   |
LL -     return true;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:35:5
   |
LL |     return true;
   |     ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return true;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:41:5
   |
LL |     return true;;;
   |     ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return true;;;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:47:5
   |
LL |     return true;; ; ;
   |     ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return true;; ; ;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:53:9
   |
LL |         return true;
   |         ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return true;
LL +         true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:56:9
   |
LL |         return false;
   |         ^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return false;
LL +         false
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:63:17
   |
LL |         true => return false,
   |                 ^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         true => return false,
LL +         true => false,
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:66:13
   |
LL |             return true;
   |             ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -             return true;
LL +             true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:74:9
   |
LL |         return true;
   |         ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return true;
LL +         true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:77:16
   |
LL |     let _ = || return true;
   |                ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     let _ = || return true;
LL +     let _ = || true;
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:82:5
   |
LL |     return the_answer!();
   |     ^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return the_answer!();
LL +     the_answer!()
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:87:5
   |
LL |     return;
   |     ^^^^^^
   |
help: remove `return`
   |
LL - fn test_void_fun() {
LL -     return;
LL + fn test_void_fun() {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:93:9
   |
LL |         return;
   |         ^^^^^^
   |
help: remove `return`
   |
LL -     if b {
LL -         return;
LL +     if b {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:96:9
   |
LL |         return;
   |         ^^^^^^
   |
help: remove `return`
   |
LL -     } else {
LL -         return;
LL +     } else {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:104:14
   |
LL |         _ => return,
   |              ^^^^^^
   |
help: replace `return` with a unit value
   |
LL -         _ => return,
LL +         _ => (),
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:114:13
   |
LL |             return;
   |             ^^^^^^
   |
help: remove `return`
   |
LL -             let _ = 42;
LL -             return;
LL +             let _ = 42;
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:117:14
   |
LL |         _ => return,
   |              ^^^^^^
   |
help: replace `return` with a unit value
   |
LL -         _ => return,
LL +         _ => (),
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:131:9
   |
LL |         return String::from("test");
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return String::from("test");
LL +         String::from("test")
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:134:9
   |
LL |         return String::new();
   |         ^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return String::new();
LL +         String::new()
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:157:32
   |
LL |         bar.unwrap_or_else(|_| return)
   |                                ^^^^^^
   |
help: replace `return` with an empty block
   |
LL -         bar.unwrap_or_else(|_| return)
LL +         bar.unwrap_or_else(|_| {})
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:163:13
   |
LL |             return;
   |             ^^^^^^
   |
help: remove `return`
   |
LL -         let _ = || {
LL -             return;
LL +         let _ = || {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:166:20
   |
LL |         let _ = || return;
   |                    ^^^^^^
   |
help: replace `return` with an empty block
   |
LL -         let _ = || return;
LL +         let _ = || {};
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:173:32
   |
LL |         res.unwrap_or_else(|_| return Foo)
   |                                ^^^^^^^^^^
   |
help: remove `return`
   |
LL -         res.unwrap_or_else(|_| return Foo)
LL +         res.unwrap_or_else(|_| Foo)
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:183:5
   |
LL |     return true;
   |     ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return true;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:188:5
   |
LL |     return true;
   |     ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return true;
LL +     true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:194:9
   |
LL |         return true;
   |         ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return true;
LL +         true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:197:9
   |
LL |         return false;
   |         ^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return false;
LL +         false
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:204:17
   |
LL |         true => return false,
   |                 ^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         true => return false,
LL +         true => false,
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:207:13
   |
LL |             return true;
   |             ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -             return true;
LL +             true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:215:9
   |
LL |         return true;
   |         ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return true;
LL +         true
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:218:16
   |
LL |     let _ = || return true;
   |                ^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     let _ = || return true;
LL +     let _ = || true;
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:223:5
   |
LL |     return the_answer!();
   |     ^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return the_answer!();
LL +     the_answer!()
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:228:5
   |
LL |     return;
   |     ^^^^^^
   |
help: remove `return`
   |
LL - async fn async_test_void_fun() {
LL -     return;
LL + async fn async_test_void_fun() {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:234:9
   |
LL |         return;
   |         ^^^^^^
   |
help: remove `return`
   |
LL -     if b {
LL -         return;
LL +     if b {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:237:9
   |
LL |         return;
   |         ^^^^^^
   |
help: remove `return`
   |
LL -     } else {
LL -         return;
LL +     } else {
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:245:14
   |
LL |         _ => return,
   |              ^^^^^^
   |
help: replace `return` with a unit value
   |
LL -         _ => return,
LL +         _ => (),
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:259:9
   |
LL |         return String::from("test");
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return String::from("test");
LL +         String::from("test")
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:262:9
   |
LL |         return String::new();
   |         ^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return String::new();
LL +         String::new()
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:279:5
   |
LL |     return format!("Hello {}", "world!");
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return format!("Hello {}", "world!");
LL +     format!("Hello {}", "world!")
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:321:9
   |
LL |         return true;
   |         ^^^^^^^^^^^
   |
help: remove `return`
   |
LL ~         true
LL |
...
LL |
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:324:9
   |
LL |         return false;
   |         ^^^^^^^^^^^^
   |
help: remove `return`
   |
LL ~         false
LL |
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:332:13
   |
LL |             return 10;
   |             ^^^^^^^^^
   |
help: remove `return`
   |
LL ~             10
LL |
...
LL |         },
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:336:13
   |
LL |             return 100;
   |             ^^^^^^^^^^
   |
help: remove `return`
   |
LL ~             100
LL |
LL |         },
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:345:9
   |
LL |         return 0;
   |         ^^^^^^^^
   |
help: remove `return`
   |
LL ~         0
LL |
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:353:13
   |
LL |             return *(x as *const isize);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL ~             *(x as *const isize)
LL |
...
LL |
LL ~         }
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:356:13
   |
LL |             return !*(x as *const isize);
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL ~             !*(x as *const isize)
LL |
LL ~         }
LL ~     }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:365:9
   |
LL |         return;
   |         ^^^^^^
   |
help: remove `return`
   |
LL -         let _ = 42;
LL -         return;
LL +         let _ = 42;
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:371:21
   |
LL |         let _ = 42; return;
   |                     ^^^^^^
   |
help: remove `return`
   |
LL -         let _ = 42; return;
LL +         let _ = 42;
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:384:9
   |
LL |         return Ok(format!("ok!"));
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return Ok(format!("ok!"));
LL +         Ok(format!("ok!"))
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:387:9
   |
LL |         return Err(format!("err!"));
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return Err(format!("err!"));
LL +         Err(format!("err!"))
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:394:9
   |
LL |         return if true { 1 } else { 2 };
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -         return if true { 1 } else { 2 };
LL +         if true { 1 } else { 2 }
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:399:9
   |
LL |         return if b1 { 0 } else { 1 } | if b2 { 2 } else { 3 } | if b3 { 4 } else { 5 };
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return` and wrap the sequence with parentheses
   |
LL -         return if b1 { 0 } else { 1 } | if b2 { 2 } else { 3 } | if b3 { 4 } else { 5 };
LL +         (if b1 { 0 } else { 1 } | if b2 { 2 } else { 3 } | if b3 { 4 } else { 5 })
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:421:5
   |
LL |     return { "a".to_string() } + "b" + { "c" };
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return` and wrap the sequence with parentheses
   |
LL -     return { "a".to_string() } + "b" + { "c" };
LL +     ({ "a".to_string() } + "b" + { "c" })
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:426:5
   |
LL |     return "".split("").next().unwrap().to_string();
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return`
   |
LL -     return "".split("").next().unwrap().to_string();
LL +     "".split("").next().unwrap().to_string()
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:461:5
   |
LL |     return unsafe { todo() } as *const i32;
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: remove `return` and wrap the sequence with parentheses
   |
LL -     return unsafe { todo() } as *const i32;
LL +     (unsafe { todo() } as *const i32)
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:468:13
   |
LL |             return 1;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 1;
LL +             1
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:471:13
   |
LL |             return 2;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 2;
LL +             2
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:474:13
   |
LL |             return 3;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 3;
LL +             3
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:481:13
   |
LL |             return 1;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 1;
LL +             1
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:486:13
   |
LL |             return 3;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 3;
LL +             3
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:493:13
   |
LL |             return 1;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 1;
LL +             1
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:498:13
   |
LL |             return 3;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 3;
LL +             3
   |

error: unneeded `return` statement
  --> tests/ui/needless_return.rs:506:13
   |
LL |             return 1;
   |             ^^^^^^^^
   |
help: remove `return`
   |
LL -             return 1;
LL +             1
   |

error: aborting due to 63 previous errors

