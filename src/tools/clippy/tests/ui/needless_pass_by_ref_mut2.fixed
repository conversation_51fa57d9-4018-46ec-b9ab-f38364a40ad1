// If both `inner_async3` and `inner_async4` are present, aliases are declared after
// they're used in `inner_async4` for some reasons... This test ensures that no
// only `v` is marked as not used mutably in `inner_async4`.

#![allow(clippy::redundant_closure_call)]
#![warn(clippy::needless_pass_by_ref_mut)]

async fn inner_async3(x: &i32, y: &mut u32) {
    //~^ needless_pass_by_ref_mut

    async {
        *y += 1;
    }
    .await;
}

async fn inner_async4(u: &mut i32, v: &u32) {
    //~^ needless_pass_by_ref_mut

    async {
        *u += 1;
    }
    .await;
}

fn main() {}
