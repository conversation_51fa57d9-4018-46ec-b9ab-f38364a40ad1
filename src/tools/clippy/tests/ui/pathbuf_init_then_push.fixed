#![warn(clippy::pathbuf_init_then_push)]

use std::path::PathBuf;

fn main() {
    let mut path_buf = PathBuf::from("foo");

    path_buf = PathBuf::from("foo").join("bar");

    let bar = "bar";
    path_buf = PathBuf::from("foo").join(bar);

    let mut path_buf = PathBuf::from("foo").join("bar").join("buz");

    let mut x = PathBuf::new();
    println!("{}", x.display());
    x.push("Duck");

    let mut path_buf = PathBuf::new();
    #[cfg(cats)]
    path_buf.push("foo");
}
