error: calling `Mutex::lock` inside the scope of another `Mutex::lock` causes a deadlock
  --> tests/ui/if_let_mutex.rs:17:5
   |
LL |       if let Err(locked) = m.lock() {
   |       ^                    - this Mutex will remain locked for the entire `if let`-block...
   |  _____|
   | |
LL | |
LL | |         do_stuff(locked);
LL | |     } else {
LL | |         let lock = m.lock().unwrap();
   | |                    - ... and is tried to lock again here, which will always deadlock.
LL | |         do_stuff(lock);
LL | |     };
   | |_____^
   |
   = help: move the lock call outside of the `if let ...` expression
   = note: `-D clippy::if-let-mutex` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::if_let_mutex)]`

error: calling `Mutex::lock` inside the scope of another `Mutex::lock` causes a deadlock
  --> tests/ui/if_let_mutex.rs:30:5
   |
LL |       if let Some(locked) = m.lock().unwrap().deref() {
   |       ^                     - this Mutex will remain locked for the entire `if let`-block...
   |  _____|
   | |
LL | |
LL | |         do_stuff(locked);
LL | |     } else {
LL | |         let lock = m.lock().unwrap();
   | |                    - ... and is tried to lock again here, which will always deadlock.
LL | |         do_stuff(lock);
LL | |     };
   | |_____^
   |
   = help: move the lock call outside of the `if let ...` expression

error: calling `Mutex::lock` inside the scope of another `Mutex::lock` causes a deadlock
  --> tests/ui/if_let_mutex.rs:52:5
   |
LL |       if let Ok(i) = mutex.lock() {
   |       ^              ----- this Mutex will remain locked for the entire `if let`-block...
   |  _____|
   | |
LL | |
LL | |         do_stuff(i);
LL | |     } else {
LL | |         let _x = mutex.lock();
   | |                  ----- ... and is tried to lock again here, which will always deadlock.
LL | |     };
   | |_____^
   |
   = help: move the lock call outside of the `if let ...` expression

error: calling `Mutex::lock` inside the scope of another `Mutex::lock` causes a deadlock
  --> tests/ui/if_let_mutex.rs:61:5
   |
LL |       if let Ok(_) = m1.lock() {
   |       ^              -- this Mutex will remain locked for the entire `if let`-block...
   |  _____|
   | |
LL | |         m2.lock();
LL | |     } else {
LL | |         m1.lock();
   | |         -- ... and is tried to lock again here, which will always deadlock.
LL | |     }
   | |_____^
   |
   = help: move the lock call outside of the `if let ...` expression

error: aborting due to 4 previous errors

