error: invisible character detected
  --> tests/ui/unicode.rs:5:12
   |
LL |     print!("Here >​< is a ZWS, and ​another");
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider replacing the string with: `"Here >\u{200B}< is a ZWS, and \u{200B}another"`
   |
   = note: `-D clippy::invisible-characters` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::invisible_characters)]`

error: invisible character detected
  --> tests/ui/unicode.rs:8:12
   |
LL |     print!("Here >­< is a SHY, and ­another");
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider replacing the string with: `"Here >\u{AD}< is a SHY, and \u{AD}another"`

error: invisible character detected
  --> tests/ui/unicode.rs:11:12
   |
LL |     print!("Here >⁠< is a WJ, and ⁠another");
   |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider replacing the string with: `"Here >\u{2060}< is a WJ, and \u{2060}another"`

error: non-NFC Unicode sequence detected
  --> tests/ui/unicode.rs:18:12
   |
LL |     print!("̀àh?");
   |            ^^^^^ help: consider replacing the string with: `"̀àh?"`
   |
   = note: `-D clippy::unicode-not-nfc` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::unicode_not_nfc)]`

error: literal non-ASCII character detected
  --> tests/ui/unicode.rs:27:16
   |
LL |         print!("Üben!");
   |                ^^^^^^^ help: consider replacing the string with: `"\u{dc}ben!"`
   |
note: the lint level is defined here
  --> tests/ui/unicode.rs:24:13
   |
LL |     #![deny(clippy::non_ascii_literal)]
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^

error: literal non-ASCII character detected
  --> tests/ui/unicode.rs:34:36
   |
LL |         const _EMPTY_BLOCK: char = '▱';
   |                                    ^^^ help: consider replacing the string with: `'\u{25b1}'`

error: literal non-ASCII character detected
  --> tests/ui/unicode.rs:36:35
   |
LL |         const _FULL_BLOCK: char = '▰';
   |                                   ^^^ help: consider replacing the string with: `'\u{25b0}'`

error: literal non-ASCII character detected
  --> tests/ui/unicode.rs:57:21
   |
LL |             let _ = "悲しいかな、ここに日本語を書くことはできない。";
   |                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider replacing the string with: `"\u{60b2}\u{3057}\u{3044}\u{304b}\u{306a}\u{3001}\u{3053}\u{3053}\u{306b}\u{65e5}\u{672c}\u{8a9e}\u{3092}\u{66f8}\u{304f}\u{3053}\u{3068}\u{306f}\u{3067}\u{304d}\u{306a}\u{3044}\u{3002}"`
   |
note: the lint level is defined here
  --> tests/ui/unicode.rs:46:17
   |
LL |         #![deny(clippy::non_ascii_literal)]
   |                 ^^^^^^^^^^^^^^^^^^^^^^^^^

error: aborting due to 8 previous errors

