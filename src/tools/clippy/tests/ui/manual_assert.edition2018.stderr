error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:32:5
   |
LL | /     if !a.is_empty() {
LL | |
LL | |         panic!("qaqaq{:?}", a);
LL | |     }
   | |_____^
   |
   = note: `-D clippy::manual-assert` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::manual_assert)]`
help: try instead
   |
LL -     if !a.is_empty() {
LL -
LL -         panic!("qaqaq{:?}", a);
LL -     }
LL +     assert!(a.is_empty(), "qaqaq{:?}", a);
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:36:5
   |
LL | /     if !a.is_empty() {
LL | |
LL | |         panic!("qwqwq");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if !a.is_empty() {
LL -
LL -         panic!("qwqwq");
LL -     }
LL +     assert!(a.is_empty(), "qwqwq");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:54:5
   |
LL | /     if b.is_empty() {
LL | |
LL | |         panic!("panic1");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if b.is_empty() {
LL -
LL -         panic!("panic1");
LL -     }
LL +     assert!(!b.is_empty(), "panic1");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:58:5
   |
LL | /     if b.is_empty() && a.is_empty() {
LL | |
LL | |         panic!("panic2");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if b.is_empty() && a.is_empty() {
LL -
LL -         panic!("panic2");
LL -     }
LL +     assert!(!(b.is_empty() && a.is_empty()), "panic2");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:62:5
   |
LL | /     if a.is_empty() && !b.is_empty() {
LL | |
LL | |         panic!("panic3");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if a.is_empty() && !b.is_empty() {
LL -
LL -         panic!("panic3");
LL -     }
LL +     assert!(!(a.is_empty() && !b.is_empty()), "panic3");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:66:5
   |
LL | /     if b.is_empty() || a.is_empty() {
LL | |
LL | |         panic!("panic4");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if b.is_empty() || a.is_empty() {
LL -
LL -         panic!("panic4");
LL -     }
LL +     assert!(!(b.is_empty() || a.is_empty()), "panic4");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:70:5
   |
LL | /     if a.is_empty() || !b.is_empty() {
LL | |
LL | |         panic!("panic5");
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if a.is_empty() || !b.is_empty() {
LL -
LL -         panic!("panic5");
LL -     }
LL +     assert!(!(a.is_empty() || !b.is_empty()), "panic5");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:74:5
   |
LL | /     if a.is_empty() {
LL | |
LL | |         panic!("with expansion {}", one!())
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if a.is_empty() {
LL -
LL -         panic!("with expansion {}", one!())
LL -     }
LL +     assert!(!a.is_empty(), "with expansion {}", one!());
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:87:5
   |
LL | /     if a > 2 {
LL | |
LL | |         // comment
LL | |         /* this is a
...  |
LL | |         panic!("panic with comment") // comment after `panic!`
LL | |     }
   | |_____^
   |
help: try instead
   |
LL -     if a > 2 {
LL -
LL -         // comment
LL -         /* this is a
LL -         multiline
LL -         comment */
LL -         /// Doc comment
LL -         panic!("panic with comment") // comment after `panic!`
LL -     }
LL +     assert!(!(a > 2), "panic with comment");
   |

error: only a `panic!` in `if`-then statement
  --> tests/ui/manual_assert.rs:102:25
   |
LL |           const BAR: () = if N == 0 {
   |  _________________________^
LL | |
LL | |             panic!()
LL | |         };
   | |_________^
   |
help: try instead
   |
LL -         const BAR: () = if N == 0 {
LL -
LL -             panic!()
LL -         };
LL +         const BAR: () = assert!(!(N == 0), );
   |

error: aborting due to 10 previous errors

