error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:5:13
   |
LL |     assert!(matches!('x', 'a'..='z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_lowercase()`
   |
   = note: `-D clippy::manual-is-ascii-check` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::manual_is_ascii_check)]`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:7:13
   |
LL |     assert!(matches!('X', 'A'..='Z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'X'.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:9:13
   |
LL |     assert!(matches!(b'x', b'a'..=b'z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'x'.is_ascii_lowercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:11:13
   |
LL |     assert!(matches!(b'X', b'A'..=b'Z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'X'.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:15:13
   |
LL |     assert!(matches!(num, '0'..='9'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `num.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:17:13
   |
LL |     assert!(matches!(b'1', b'0'..=b'9'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'1'.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:19:13
   |
LL |     assert!(matches!('x', 'A'..='Z' | 'a'..='z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_alphabetic()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:24:5
   |
LL |     (b'0'..=b'9').contains(&b'0');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'0'.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:26:5
   |
LL |     (b'a'..=b'z').contains(&b'a');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'a'.is_ascii_lowercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:28:5
   |
LL |     (b'A'..=b'Z').contains(&b'A');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'A'.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:31:5
   |
LL |     ('0'..='9').contains(&'0');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'0'.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:33:5
   |
LL |     ('a'..='z').contains(&'a');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'a'.is_ascii_lowercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:35:5
   |
LL |     ('A'..='Z').contains(&'A');
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'A'.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:39:5
   |
LL |     ('0'..='9').contains(cool_letter);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `cool_letter.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:41:5
   |
LL |     ('a'..='z').contains(cool_letter);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `cool_letter.is_ascii_lowercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:43:5
   |
LL |     ('A'..='Z').contains(cool_letter);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `cool_letter.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:57:13
   |
LL |     assert!(matches!(b'1', b'0'..=b'9'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `b'1'.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:59:13
   |
LL |     assert!(matches!('X', 'A'..='Z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'X'.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:61:13
   |
LL |     assert!(matches!('x', 'A'..='Z' | 'a'..='z'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_alphabetic()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:63:13
   |
LL |     assert!(matches!('x', '0'..='9' | 'a'..='f' | 'A'..='F'));
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_hexdigit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:75:23
   |
LL |     const FOO: bool = matches!('x', '0'..='9');
   |                       ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:77:23
   |
LL |     const BAR: bool = matches!('x', '0'..='9' | 'a'..='f' | 'A'..='F');
   |                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `'x'.is_ascii_hexdigit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:84:5
   |
LL |     ('0'..='9').contains(&&cool_letter);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `cool_letter.is_ascii_digit()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:86:5
   |
LL |     ('a'..='z').contains(*cool_letter);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `cool_letter.is_ascii_lowercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:105:20
   |
LL |     take_while(|c| ('A'..='Z').contains(&c));
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: try
   |
LL -     take_while(|c| ('A'..='Z').contains(&c));
LL +     take_while(|c: char| c.is_ascii_uppercase());
   |

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:107:20
   |
LL |     take_while(|c| (b'A'..=b'Z').contains(&c));
   |                    ^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
help: try
   |
LL -     take_while(|c| (b'A'..=b'Z').contains(&c));
LL +     take_while(|c: u8| c.is_ascii_uppercase());
   |

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:109:26
   |
LL |     take_while(|c: char| ('A'..='Z').contains(&c));
   |                          ^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `c.is_ascii_uppercase()`

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:114:63
   |
LL |     let digits: Vec<&char> = ['1', 'A'].iter().take_while(|c| ('0'..='9').contains(c)).collect();
   |                                                               ^^^^^^^^^^^^^^^^^^^^^^^
   |
help: try
   |
LL -     let digits: Vec<&char> = ['1', 'A'].iter().take_while(|c| ('0'..='9').contains(c)).collect();
LL +     let digits: Vec<&char> = ['1', 'A'].iter().take_while(|c: &&char| c.is_ascii_digit()).collect();
   |

error: manual check for common ascii range
  --> tests/ui/manual_is_ascii_check.rs:116:71
   |
LL |     let digits: Vec<&mut char> = ['1', 'A'].iter_mut().take_while(|c| ('0'..='9').contains(c)).collect();
   |                                                                       ^^^^^^^^^^^^^^^^^^^^^^^
   |
help: try
   |
LL -     let digits: Vec<&mut char> = ['1', 'A'].iter_mut().take_while(|c| ('0'..='9').contains(c)).collect();
LL +     let digits: Vec<&mut char> = ['1', 'A'].iter_mut().take_while(|c: &&mut char| c.is_ascii_digit()).collect();
   |

error: aborting due to 29 previous errors

