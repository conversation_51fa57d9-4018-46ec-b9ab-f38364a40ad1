error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:55:19
   |
LL |     let b: &str = a.deref();
   |                   ^^^^^^^^^ help: try: `&*a`
   |
   = note: `-D clippy::explicit-deref-methods` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::explicit_deref_methods)]`

error: explicit `deref_mut` method call
  --> tests/ui/explicit_deref_methods.rs:58:23
   |
LL |     let b: &mut str = a.deref_mut();
   |                       ^^^^^^^^^^^^^ help: try: `&mut **a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:62:39
   |
LL |     let b: String = format!("{}, {}", a.deref(), a.deref());
   |                                       ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:62:50
   |
LL |     let b: String = format!("{}, {}", a.deref(), a.deref());
   |                                                  ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:66:20
   |
LL |     println!("{}", a.deref());
   |                    ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:70:11
   |
LL |     match a.deref() {
   |           ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:75:28
   |
LL |     let b: String = concat(a.deref());
   |                            ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:78:13
   |
LL |     let b = just_return(a).deref();
   |             ^^^^^^^^^^^^^^^^^^^^^^ help: try: `just_return(a)`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:81:28
   |
LL |     let b: String = concat(just_return(a).deref());
   |                            ^^^^^^^^^^^^^^^^^^^^^^ help: try: `just_return(a)`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:121:31
   |
LL |     let b: &str = expr_deref!(a.deref());
   |                               ^^^^^^^^^ help: try: `&*a`

error: explicit `deref` method call
  --> tests/ui/explicit_deref_methods.rs:139:14
   |
LL |     let _ = &Deref::deref(&"foo");
   |              ^^^^^^^^^^^^^^^^^^^^ help: try: `*&"foo"`

error: explicit `deref_mut` method call
  --> tests/ui/explicit_deref_methods.rs:141:14
   |
LL |     let _ = &DerefMut::deref_mut(&mut x);
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `&mut **&mut x`

error: explicit `deref_mut` method call
  --> tests/ui/explicit_deref_methods.rs:142:14
   |
LL |     let _ = &DerefMut::deref_mut((&mut &mut x).deref_mut());
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: try: `&mut ***(&mut &mut x)`

error: aborting due to 13 previous errors

