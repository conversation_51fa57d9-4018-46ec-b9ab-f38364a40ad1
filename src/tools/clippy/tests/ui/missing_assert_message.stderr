error: assert without any message
  --> tests/ui/missing_assert_message.rs:12:5
   |
LL |     assert!(foo());
   |     ^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic
   = note: `-D clippy::missing-assert-message` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::missing_assert_message)]`

error: assert without any message
  --> tests/ui/missing_assert_message.rs:14:5
   |
LL |     assert_eq!(foo(), foo());
   |     ^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:16:5
   |
LL |     assert_ne!(foo(), foo());
   |     ^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:18:5
   |
LL |     debug_assert!(foo());
   |     ^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:20:5
   |
LL |     debug_assert_eq!(foo(), foo());
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:22:5
   |
LL |     debug_assert_ne!(foo(), foo());
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:28:5
   |
LL |     assert!(bar!(true));
   |     ^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:30:5
   |
LL |     assert!(bar!(true, false));
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:32:5
   |
LL |     assert_eq!(bar!(true), foo());
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:34:5
   |
LL |     assert_ne!(bar!(true, true), bar!(true));
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:40:5
   |
LL |     assert!(foo(),);
   |     ^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:42:5
   |
LL |     assert_eq!(foo(), foo(),);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:44:5
   |
LL |     assert_ne!(foo(), foo(),);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:46:5
   |
LL |     debug_assert!(foo(),);
   |     ^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:48:5
   |
LL |     debug_assert_eq!(foo(), foo(),);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: assert without any message
  --> tests/ui/missing_assert_message.rs:50:5
   |
LL |     debug_assert_ne!(foo(), foo(),);
   |     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
   |
   = help: consider describing why the failing assert is problematic

error: aborting due to 16 previous errors

