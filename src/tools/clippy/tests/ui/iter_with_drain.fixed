// will emits unused mut warnings after fixing
#![allow(unused_mut)]
// will emits needless collect warnings after fixing
#![allow(clippy::needless_collect, clippy::drain_collect)]
#![warn(clippy::iter_with_drain)]
use std::collections::{BinaryHeap, HashMap, HashSet, VecDeque};

fn full() {
    let mut a = vec!["aaa".to_string(), "bbb".to_string()];
    let mut a: BinaryHeap<_> = a.into_iter().collect();
    //~^ iter_with_drain
    let mut a: HashSet<_> = a.drain().collect();
    let mut a: VecDeque<_> = a.drain().collect();
    let mut a: Vec<_> = a.into_iter().collect();
    //~^ iter_with_drain
    let mut a: HashMap<_, _> = a.into_iter().map(|x| (x.clone(), x)).collect();
    //~^ iter_with_drain
    let _: Vec<(String, String)> = a.drain().collect();
}

fn closed() {
    let mut a = vec!["aaa".to_string(), "bbb".to_string()];
    let mut a: BinaryHeap<_> = a.into_iter().collect();
    //~^ iter_with_drain
    let mut a: HashSet<_> = a.drain().collect();
    let mut a: VecDeque<_> = a.drain().collect();
    let mut a: Vec<_> = a.into_iter().collect();
    //~^ iter_with_drain
    let mut a: HashMap<_, _> = a.into_iter().map(|x| (x.clone(), x)).collect();
    //~^ iter_with_drain
    let _: Vec<(String, String)> = a.drain().collect();
}

fn should_not_help() {
    let mut a = vec!["aaa".to_string(), "bbb".to_string()];
    let mut a: BinaryHeap<_> = a.drain(1..).collect();
    let mut a: HashSet<_> = a.drain().collect();
    let mut a: VecDeque<_> = a.drain().collect();
    let mut a: Vec<_> = a.drain(..a.len() - 1).collect();
    let mut a: HashMap<_, _> = a.drain(1..a.len() - 1).map(|x| (x.clone(), x)).collect();
    let _: Vec<(String, String)> = a.drain().collect();

    let mut b = vec!["aaa".to_string(), "bbb".to_string()];
    let _: Vec<_> = b.drain(0..a.len()).collect();
}

fn _closed_range(mut x: Vec<String>) {
    let _: Vec<String> = x.drain(0..=x.len()).collect();
}

fn _with_mut(x: &mut Vec<String>, y: &mut VecDeque<String>) {
    let _: Vec<String> = x.drain(..).collect();
    let _: Vec<String> = y.drain(..).collect();
}

#[derive(Default)]
struct Bomb {
    fire: Vec<u8>,
}

fn should_not_help_0(bomb: &mut Bomb) {
    let _: Vec<u8> = bomb.fire.drain(..).collect();
}

fn main() {
    full();
    closed();
    should_not_help();
    should_not_help_0(&mut Bomb::default());
}
