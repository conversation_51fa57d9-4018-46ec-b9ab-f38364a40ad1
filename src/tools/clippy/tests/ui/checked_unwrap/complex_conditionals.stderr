error: called `unwrap` on `x` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:13:9
   |
LL |     if x.is_ok() && y.is_err() {
   |        --------- the check is happening here
LL |         // unnecessary
LL |         x.unwrap();
   |         ^^^^^^^^^^
   |
   = help: try using `if let` or `match`
note: the lint level is defined here
  --> tests/ui/checked_unwrap/complex_conditionals.rs:1:35
   |
LL | #![deny(clippy::panicking_unwrap, clippy::unnecessary_unwrap)]
   |                                   ^^^^^^^^^^^^^^^^^^^^^^^^^^

error: this call to `unwrap_err()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:17:9
   |
LL |     if x.is_ok() && y.is_err() {
   |        --------- because of this check
...
LL |         x.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
note: the lint level is defined here
  --> tests/ui/checked_unwrap/complex_conditionals.rs:1:9
   |
LL | #![deny(clippy::panicking_unwrap, clippy::unnecessary_unwrap)]
   |         ^^^^^^^^^^^^^^^^^^^^^^^^

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:21:9
   |
LL |     if x.is_ok() && y.is_err() {
   |                     ---------- because of this check
...
LL |         y.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `y` after checking its variant with `is_err`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:25:9
   |
LL |     if x.is_ok() && y.is_err() {
   |                     ---------- the check is happening here
...
LL |         y.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:41:9
   |
LL |     if x.is_ok() || y.is_ok() {
   |        --------- because of this check
...
LL |         x.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `x` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:45:9
   |
LL |     if x.is_ok() || y.is_ok() {
   |        --------- the check is happening here
...
LL |         x.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:49:9
   |
LL |     if x.is_ok() || y.is_ok() {
   |                     --------- because of this check
...
LL |         y.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `y` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:53:9
   |
LL |     if x.is_ok() || y.is_ok() {
   |                     --------- the check is happening here
...
LL |         y.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: called `unwrap` on `x` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:59:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |        --------- the check is happening here
LL |         // unnecessary
LL |         x.unwrap();
   |         ^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: this call to `unwrap_err()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:63:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |        --------- because of this check
...
LL |         x.unwrap_err();
   |         ^^^^^^^^^^^^^^

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:67:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |                       --------- because of this check
...
LL |         y.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `y` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:71:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |                       --------- the check is happening here
...
LL |         y.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: called `unwrap` on `z` after checking its variant with `is_err`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:75:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |                                    ---------- the check is happening here
...
LL |         z.unwrap();
   |         ^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: this call to `unwrap_err()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:79:9
   |
LL |     if x.is_ok() && !(y.is_ok() || z.is_err()) {
   |                                    ---------- because of this check
...
LL |         z.unwrap_err();
   |         ^^^^^^^^^^^^^^

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:89:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |        --------- because of this check
...
LL |         x.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `x` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:93:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |        --------- the check is happening here
...
LL |         x.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: called `unwrap` on `y` after checking its variant with `is_ok`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:97:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |                       --------- the check is happening here
...
LL |         y.unwrap();
   |         ^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: this call to `unwrap_err()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:101:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |                       --------- because of this check
...
LL |         y.unwrap_err();
   |         ^^^^^^^^^^^^^^

error: this call to `unwrap()` will always panic
  --> tests/ui/checked_unwrap/complex_conditionals.rs:105:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |                                    ---------- because of this check
...
LL |         z.unwrap();
   |         ^^^^^^^^^^

error: called `unwrap_err` on `z` after checking its variant with `is_err`
  --> tests/ui/checked_unwrap/complex_conditionals.rs:109:9
   |
LL |     if x.is_ok() || !(y.is_ok() && z.is_err()) {
   |                                    ---------- the check is happening here
...
LL |         z.unwrap_err();
   |         ^^^^^^^^^^^^^^
   |
   = help: try using `if let` or `match`

error: aborting due to 20 previous errors

