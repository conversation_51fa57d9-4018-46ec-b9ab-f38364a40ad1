error: calling `as_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:17:14
   |
LL |     let bs = "hello there".as_bytes();
   |              ^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using a byte string literal instead: `b"hello there"`
   |
   = note: `-D clippy::string-lit-as-bytes` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::string_lit_as_bytes)]`

error: calling `as_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:20:14
   |
LL |     let bs = r###"raw string with 3# plus " ""###.as_bytes();
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using a byte string literal instead: `br###"raw string with 3# plus " ""###`

error: calling `into_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:23:14
   |
LL |     let bs = "lit to string".to_string().into_bytes();
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using a byte string literal instead: `b"lit to string".to_vec()`

error: calling `into_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:25:14
   |
LL |     let bs = "lit to owned".to_owned().into_bytes();
   |              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using a byte string literal instead: `b"lit to owned".to_vec()`

error: calling `as_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:11:26
   |
LL |         const B: &[u8] = $b.as_bytes();
   |                          ^^^^^^^^^^^^^ help: consider using a byte string literal instead: `b"warning"`
...
LL |     b!("warning");
   |     ------------- in this macro invocation
   |
   = note: this error originates in the macro `b` (in Nightly builds, run with -Z macro-backtrace for more info)

error: calling `as_bytes()` on `include_str!(..)`
  --> tests/ui/string_lit_as_bytes.rs:43:22
   |
LL |     let includestr = include_str!("string_lit_as_bytes.rs").as_bytes();
   |                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using `include_bytes!(..)` instead: `include_bytes!("string_lit_as_bytes.rs")`

error: calling `as_bytes()` on a string literal
  --> tests/ui/string_lit_as_bytes.rs:46:13
   |
LL |     let _ = "string with newline\t\n".as_bytes();
   |             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ help: consider using a byte string literal instead: `b"string with newline\t\n"`

error: aborting due to 7 previous errors

