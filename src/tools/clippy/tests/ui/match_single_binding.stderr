error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:33:5
   |
LL | /     match (a, b, c) {
LL | |
LL | |         (x, y, z) => {
LL | |             println!("{} {} {}", x, y, z);
LL | |         },
LL | |     }
   | |_____^
   |
   = note: `-D clippy::match-single-binding` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::match_single_binding)]`
help: consider using a `let` statement
   |
LL ~     let (x, y, z) = (a, b, c);
LL +     {
LL +         println!("{} {} {}", x, y, z);
LL +     }
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:40:5
   |
LL | /     match (a, b, c) {
LL | |
LL | |         (x, y, z) => println!("{} {} {}", x, y, z),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let (x, y, z) = (a, b, c);
LL +     println!("{} {} {}", x, y, z);
   |

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:58:5
   |
LL | /     match a {
LL | |
LL | |         _ => println!("whatever"),
LL | |     }
   | |_____^ help: consider using the match body instead: `println!("whatever");`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:63:5
   |
LL | /     match a {
LL | |
LL | |         _ => {
LL | |             let x = 29;
LL | |             println!("x has a value of {}", x);
LL | |         },
LL | |     }
   | |_____^
   |
help: consider using the match body instead
   |
LL ~     {
LL +         let x = 29;
LL +         println!("x has a value of {}", x);
LL +     }
   |

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:71:5
   |
LL | /     match a {
LL | |
LL | |         _ => {
LL | |             let e = 5 * a;
...  |
LL | |         },
LL | |     }
   | |_____^
   |
help: consider using the match body instead
   |
LL ~     {
LL +         let e = 5 * a;
LL +         if e >= 5 {
LL +             println!("e is superior to 5");
LL +         }
LL +     }
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:82:5
   |
LL | /     match p {
LL | |
LL | |         Point { x, y } => println!("Coords: ({}, {})", x, y),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let Point { x, y } = p;
LL +     println!("Coords: ({}, {})", x, y);
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:87:5
   |
LL | /     match p {
LL | |
LL | |         Point { x: x1, y: y1 } => println!("Coords: ({}, {})", x1, y1),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let Point { x: x1, y: y1 } = p;
LL +     println!("Coords: ({}, {})", x1, y1);
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:93:5
   |
LL | /     match x {
LL | |
LL | |         ref r => println!("Got a reference to {}", r),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let ref r = x;
LL +     println!("Got a reference to {}", r);
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:99:5
   |
LL | /     match x {
LL | |
LL | |         ref mut mr => println!("Got a mutable reference to {}", mr),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let ref mut mr = x;
LL +     println!("Got a mutable reference to {}", mr);
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:104:5
   |
LL | /     let product = match coords() {
LL | |
LL | |         Point { x, y } => x * y,
LL | |     };
   | |______^
   |
help: consider using a `let` statement
   |
LL ~     let Point { x, y } = coords();
LL +     let product = x * y;
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:113:18
   |
LL |           .map(|i| match i.unwrap() {
   |  __________________^
LL | |
LL | |             unwrapped => unwrapped,
LL | |         })
   | |_________^
   |
help: consider using a `let` statement
   |
LL ~         .map(|i| {
LL +             let unwrapped = i.unwrap();
LL +             unwrapped
LL ~         })
   |

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:140:5
   |
LL | /     match x {
LL | |
LL | |         // =>
LL | |         _ => println!("Not an array index start"),
LL | |     }
   | |_____^ help: consider using the match body instead: `println!("Not an array index start")`

error: this assignment could be simplified
  --> tests/ui/match_single_binding.rs:150:5
   |
LL | /     val = match val.split_at(idx) {
LL | |
LL | |         (pre, suf) => {
LL | |             println!("{}", pre);
LL | |             suf
LL | |         },
LL | |     };
   | |_____^
   |
help: consider removing the `match` expression
   |
LL ~     let (pre, suf) = val.split_at(idx);
LL +     val = {
LL +         println!("{}", pre);
LL +         suf
LL ~     };
   |

error: this match could be replaced by its scrutinee and body
  --> tests/ui/match_single_binding.rs:164:16
   |
LL |       let _ = || match side_effects() {
   |  ________________^
LL | |
LL | |         _ => println!("Needs curlies"),
LL | |     };
   | |_____^
   |
help: consider using the scrutinee and body instead
   |
LL ~     let _ = || {
LL +         side_effects();
LL +         println!("Needs curlies")
LL ~     };
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:171:5
   |
LL | /     match r {
LL | |
LL | |         x => match x {
LL | |             Some(_) => {
...  |
LL | |         },
LL | |     };
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let x = r;
LL +     match x {
LL +         Some(_) => {
LL +             println!("Some");
LL +         },
LL +         None => {
LL +             println!("None");
LL +         },
LL ~     };
   |

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:185:5
   |
LL | /     match 1 {
LL | |
LL | |         _ => (),
LL | |     }
   | |_____^ help: consider using the match body instead: `();`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:190:13
   |
LL |       let a = match 1 {
   |  _____________^
LL | |
LL | |         _ => (),
LL | |     };
   | |_____^ help: consider using the match body instead: `()`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:195:5
   |
LL | /     match 1 {
LL | |
LL | |         _ => side_effects(),
LL | |     }
   | |_____^ help: consider using the match body instead: `side_effects();`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:200:13
   |
LL |       let b = match 1 {
   |  _____________^
LL | |
LL | |         _ => side_effects(),
LL | |     };
   | |_____^ help: consider using the match body instead: `side_effects()`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:205:5
   |
LL | /     match 1 {
LL | |
LL | |         _ => println!("1"),
LL | |     }
   | |_____^ help: consider using the match body instead: `println!("1");`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:210:13
   |
LL |       let c = match 1 {
   |  _____________^
LL | |
LL | |         _ => println!("1"),
LL | |     };
   | |_____^ help: consider using the match body instead: `println!("1")`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:216:9
   |
LL | /         match 1 {
LL | |
LL | |             _ => (),
LL | |         },
   | |_________^ help: consider using the match body instead: `()`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:220:9
   |
LL | /         match 1 {
LL | |
LL | |             _ => side_effects(),
LL | |         },
   | |_________^ help: consider using the match body instead: `side_effects()`

error: this match could be replaced by its body itself
  --> tests/ui/match_single_binding.rs:224:9
   |
LL | /         match 1 {
LL | |
LL | |             _ => println!("1"),
LL | |         },
   | |_________^ help: consider using the match body instead: `println!("1")`

error: this match could be replaced by its scrutinee and body
  --> tests/ui/match_single_binding.rs:239:5
   |
LL | /     match dbg!(3) {
LL | |         _ => println!("here"),
LL | |     }
   | |_____^
   |
help: consider using the scrutinee and body instead
   |
LL ~     dbg!(3);
LL +     println!("here");
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:243:5
   |
LL | /     match dbg!(3) {
LL | |         id!(a) => println!("found {a}"),
LL | |     }
   | |_____^
   |
help: consider using a `let` statement
   |
LL ~     let id!(a) = dbg!(3);
LL +     println!("found {a}");
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:247:5
   |
LL | /     let id!(_a) = match dbg!(3) {
LL | |         id!(b) => dbg!(b + 1),
LL | |     };
   | |______^
   |
help: consider using a `let` statement
   |
LL ~     let id!(b) = dbg!(3);
LL +     let id!(_a) = dbg!(b + 1);
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:255:21
   |
LL |           inner: [(); match 1 {
   |  _____________________^
LL | |
LL | |             _n => 42,
LL | |         }],
   | |_________^
   |
help: consider using a `let` statement
   |
LL ~         inner: [(); {
LL +             let _n = 1;
LL +             42
LL ~         }],
   |

error: this match could be written as a `let` statement
  --> tests/ui/match_single_binding.rs:263:13
   |
LL | /             match 1 {
LL | |
LL | |                 _n => 42,
LL | |             }
   | |_____________^
   |
help: consider using a `let` statement
   |
LL ~             let _n = 1;
LL +             42
   |

error: aborting due to 29 previous errors

