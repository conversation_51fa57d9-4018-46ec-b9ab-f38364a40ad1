error: returning a `str` unnecessarily tied to the lifetime of arguments
  --> tests/ui/unnecessary_literal_bound.rs:9:30
   |
LL |     fn returns_lit(&self) -> &str {
   |                              ^^^^ help: try: `&'static str`
   |
   = note: `-D clippy::unnecessary-literal-bound` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::unnecessary_literal_bound)]`

error: returning a `str` unnecessarily tied to the lifetime of arguments
  --> tests/ui/unnecessary_literal_bound.rs:30:68
   |
LL |     fn contionally_returns_literals_explicit(&self, cond: bool) -> &str {
   |                                                                    ^^^^ help: try: `&'static str`

error: returning a `str` unnecessarily tied to the lifetime of arguments
  --> tests/ui/unnecessary_literal_bound.rs:55:31
   |
LL |     fn trait_method(&self) -> &str {
   |                               ^^^^ help: try: `&'static str`

error: aborting due to 3 previous errors

