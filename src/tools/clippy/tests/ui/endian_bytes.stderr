error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:9:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: `-D clippy::host-endian-bytes` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::host_endian_bytes)]`
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:11:9
   |
LL |         2i8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u16::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:13:9
   |
LL |         2u16.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i16::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:15:9
   |
LL |         2i16.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u32::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:17:9
   |
LL |         2u32.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i32::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:19:9
   |
LL |         2i32.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u64::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:21:9
   |
LL |         2u64.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i64::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:23:9
   |
LL |         2i64.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u128::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:25:9
   |
LL |         2u128.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i128::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:27:9
   |
LL |         2i128.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `f32::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:29:9
   |
LL |         2.0f32.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `f64::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:31:9
   |
LL |         2.0f64.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `usize::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:33:9
   |
LL |         2usize.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `isize::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:35:9
   |
LL |         2isize.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:37:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:39:9
   |
LL |         i8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u16::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:41:9
   |
LL |         u16::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i16::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:43:9
   |
LL |         i16::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u32::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:45:9
   |
LL |         u32::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i32::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:47:9
   |
LL |         i32::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u64::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:49:9
   |
LL |         u64::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i64::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:51:9
   |
LL |         i64::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u128::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:53:9
   |
LL |         u128::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i128::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:55:9
   |
LL |         i128::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `usize::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:57:9
   |
LL |         usize::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `isize::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:59:9
   |
LL |         isize::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `f32::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:61:9
   |
LL |         f32::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `f64::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:63:9
   |
LL |         f64::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host() { fn_body!(); }
   |             ---------- in this macro invocation
   |
   = help: specify the desired endianness explicitly
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:66:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: `-D clippy::little-endian-bytes` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::little_endian_bytes)]`
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:68:9
   |
LL |         2i8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u16::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:70:9
   |
LL |         2u16.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i16::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:72:9
   |
LL |         2i16.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u32::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:74:9
   |
LL |         2u32.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i32::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:76:9
   |
LL |         2i32.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u64::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:78:9
   |
LL |         2u64.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i64::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:80:9
   |
LL |         2i64.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u128::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:82:9
   |
LL |         2u128.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `i128::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:84:9
   |
LL |         2i128.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `f32::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:86:9
   |
LL |         2.0f32.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `f64::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:88:9
   |
LL |         2.0f64.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `usize::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:90:9
   |
LL |         2usize.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `isize::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:92:9
   |
LL |         2isize.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:94:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:96:9
   |
LL |         i8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u16::from_le_bytes`
  --> tests/ui/endian_bytes.rs:98:9
   |
LL |         u16::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i16::from_le_bytes`
  --> tests/ui/endian_bytes.rs:100:9
   |
LL |         i16::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u32::from_le_bytes`
  --> tests/ui/endian_bytes.rs:102:9
   |
LL |         u32::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i32::from_le_bytes`
  --> tests/ui/endian_bytes.rs:104:9
   |
LL |         i32::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u64::from_le_bytes`
  --> tests/ui/endian_bytes.rs:106:9
   |
LL |         u64::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i64::from_le_bytes`
  --> tests/ui/endian_bytes.rs:108:9
   |
LL |         i64::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u128::from_le_bytes`
  --> tests/ui/endian_bytes.rs:110:9
   |
LL |         u128::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `i128::from_le_bytes`
  --> tests/ui/endian_bytes.rs:112:9
   |
LL |         i128::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `usize::from_le_bytes`
  --> tests/ui/endian_bytes.rs:114:9
   |
LL |         usize::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `isize::from_le_bytes`
  --> tests/ui/endian_bytes.rs:116:9
   |
LL |         isize::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `f32::from_le_bytes`
  --> tests/ui/endian_bytes.rs:118:9
   |
LL |         f32::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `f64::from_le_bytes`
  --> tests/ui/endian_bytes.rs:120:9
   |
LL |         f64::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little() { fn_body!(); }
   |               ---------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:128:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_little() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use `u8::to_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:134:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_little() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use `u8::from_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_be_bytes` method
  --> tests/ui/endian_bytes.rs:154:9
   |
LL |         2u8.to_be_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_little() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use `u8::to_le_bytes` instead
   = note: `-D clippy::big-endian-bytes` implied by `-D warnings`
   = help: to override `-D warnings` add `#[allow(clippy::big_endian_bytes)]`
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_be_bytes`
  --> tests/ui/endian_bytes.rs:160:9
   |
LL |         u8::from_be_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_little() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use `u8::from_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:128:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_big() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use `u8::to_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:134:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_big() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use `u8::from_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:141:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_big() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use `u8::to_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:147:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn host_encourage_big() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use `u8::from_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:128:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:134:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:141:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:147:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_be_bytes` method
  --> tests/ui/endian_bytes.rs:154:9
   |
LL |         2u8.to_be_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_be_bytes`
  --> tests/ui/endian_bytes.rs:160:9
   |
LL |         u8::from_be_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn no_help() { fn_body_smol!(); }
   |                --------------- in this macro invocation
   |
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:141:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_host() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:147:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_host() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_be_bytes` method
  --> tests/ui/endian_bytes.rs:154:9
   |
LL |         2u8.to_be_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_host() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_be_bytes`
  --> tests/ui/endian_bytes.rs:160:9
   |
LL |         u8::from_be_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_host() { fn_body_smol!(); }
   |                              --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:128:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_big() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::to_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:134:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_big() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::from_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:141:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_big() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::to_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:147:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn little_encourage_big() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::from_be_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_le_bytes` method
  --> tests/ui/endian_bytes.rs:141:9
   |
LL |         2u8.to_le_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_host() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_le_bytes`
  --> tests/ui/endian_bytes.rs:147:9
   |
LL |         u8::from_le_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_host() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_be_bytes` method
  --> tests/ui/endian_bytes.rs:154:9
   |
LL |         2u8.to_be_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_host() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_be_bytes`
  --> tests/ui/endian_bytes.rs:160:9
   |
LL |         u8::from_be_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_host() { fn_body_smol!(); }
   |                           --------------- in this macro invocation
   |
   = help: use the native endianness instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_ne_bytes` method
  --> tests/ui/endian_bytes.rs:128:9
   |
LL |         2u8.to_ne_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_little() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::to_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_ne_bytes`
  --> tests/ui/endian_bytes.rs:134:9
   |
LL |         u8::from_ne_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_little() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::from_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the `u8::to_be_bytes` method
  --> tests/ui/endian_bytes.rs:154:9
   |
LL |         2u8.to_be_bytes();
   |         ^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_little() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::to_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: usage of the function `u8::from_be_bytes`
  --> tests/ui/endian_bytes.rs:160:9
   |
LL |         u8::from_be_bytes(todo!());
   |         ^^^^^^^^^^^^^^^^^^^^^^^^^^
...
LL | fn big_encourage_little() { fn_body_smol!(); }
   |                             --------------- in this macro invocation
   |
   = help: use `u8::from_le_bytes` instead
   = note: this error originates in the macro `fn_body_smol` (in Nightly builds, run with -Z macro-backtrace for more info)

error: aborting due to 86 previous errors

